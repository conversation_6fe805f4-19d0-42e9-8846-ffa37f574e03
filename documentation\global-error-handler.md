# global-error-handler

## Error Handler: global-error-handler

```mermaid
---
title: <PERSON><PERSON><PERSON>
---
flowchart LR
    subgraph Error Type
    B[Variable: httpStatus]
    B --> C
    C[Variable: errorDescription]
    C --> D
    D[Flow Reference: common-error-sub-flow]
    end
```

> **DEFAULTS**  
> : *On Error Propagate* propagates error after executing other components  
> : `httpStatus` variable - specific http status for error (* exception)  
> : `errorDescription` variable - specific message and details for error

### Variable: httpStatus

```json
// Variable: httpStatus
 httpStatus: ${errorCodeMessage.apikit.[HTTP_STATUS].code}
```

|Type|HTTP_STATUS|
|---|---|
|CUSTOM:INVALID_HEADER_CONTEXT|`badRequest`|
|APIKIT:BAD_REQUEST|`badRequest`|
|APIKIT:NOT_FOUND|`notFound`|
|APIKIT:METHOD_NOT_ALLOWED|`methodNotAllowed`|
|APIKIT:NOT_ACCEPTABLE|`notAcceptable`|
|APIKIT:UNSUPPORTED_MEDIA_TYPE|`unsupportedMediaType`|
|APIKIT:NOT_IMPLEMENTED|`notImplemented`|
|APIKIT:EXPRESSION*|`attributes.statusCode default "500"`|
|APIKIT:ANY*|`attributes.statusCode default "500"`|

### Variable: errorDescription

```json
// Variable: errorDescription
{
 message: "[ERROR_DESCRIPTION_MESSAGE]",
 details: [ERROR_DESCRIPTION_DETAILS]
}
```

|Type|ERROR_DESCRIPTION_MESSAGE|ERROR_DESCRIPTION_DETAILS|
|---|---|---|
|CUSTOM:INVALID_HEADER_CONTEXT|`BAD_REQUEST`|`error.description default ""`|
|APIKIT:BAD_REQUEST|`BAD_REQUEST`|`error.description default ""`|
|APIKIT:NOT_FOUND|`NOT_FOUND`|`error.description default ""`|
|APIKIT:METHOD_NOT_ALLOWED|`METHOD_NOT_ALLOWED`|`p('errorCodeMessage.apikit.methodNotAllowed.description')`|
|APIKIT:NOT_ACCEPTABLE|`NOT_ACCEPTABLE`|`p('errorCodeMessage.apikit.notAcceptable.description')`|
|APIKIT:UNSUPPORTED_MEDIA_TYPE|`UNSUPPORTED_MEDIA_TYPE`|`p('errorCodeMessage.apikit.unsupportedMediaType.description')`|
|APIKIT:NOT_IMPLEMENTED|`NOT_IMPLEMENTED`|`p('errorCodeMessage.apikit.notImplemented.description')`|
|APIKIT:EXPRESSION|`DATAWEAVE_EXPRESSION_ERROR`|`error.description default ""`|
|APIKIT:ANY|`INTERNAL_SERVER_ERROR`|`error.description default ""`|

### Flow Reference: common-error-subflow

[common-error-sub-flow](#common-error-sub-flow)

## common-error-sub-flow

### Payload: Final Error Response

```json
{
 code: vars.httpStatus,
 status: "FAILURE",
 transactionId: vars.vCorrelationId,
 response: {
  message: vars.errorDescription.message,
  details: vars.errorDescription.details
 }
}
```

### Logger: Exit Generic Error Handler

- Message: `payload`
- Level: `ERROR`
