### HTTPS Listener ###
https.listener.host=0.0.0.0
https.listener.port=8081
https.listener.readTimeout=30000
https.listener.idleTimeout=30000

# > APIKIT: SUCCESS
apikit.success.ok.code=200
apikit.success.ok.description=OK
apikit.success.created.code=201
apikit.success.created.description=CREATED

### Error definition ###
errorCodeMessage.apikit.badRequest.code=400
errorCodeMessage.apikit.badRequest.description=Bad Request
errorCodeMessage.apikit.notFound.code=404
errorCodeMessage.apikit.notFound.description=Not Found
errorCodeMessage.apikit.methodNotAllowed.code=405
errorCodeMessage.apikit.methodNotAllowed.description=Method Not Allowed
errorCodeMessage.apikit.notAcceptable.code=406
errorCodeMessage.apikit.notAcceptable.description=Not Acceptable
errorCodeMessage.apikit.unsupportedMediaType.code=415
errorCodeMessage.apikit.unsupportedMediaType.description=Unsupported Media Type
errorCodeMessage.apikit.notImplemented.code=501
errorCodeMessage.apikit.notImplemented.description=Not Implemented