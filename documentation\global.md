# global

![global](https://github.com/3DegreesGroupInc/Apisero_Artemis_SYS_API/blob/feature/documentation/global.png)

> properties used in global elements are defined in CONFIGURATION `.properties` files

## HTTPS_Listener_config

```xml
<http:listener-config name="HTTPS_Listener_config"
  doc:name="HTTP Listener config"
  doc:id="3af8c438-e716-478b-b6aa-fa1727eb12f0">
  <http:listener-connection
   host="${https.listener.host}" port="${https.listener.port}"
   readTimeout="${https.listener.readTimeout}"
   connectionIdleTimeout="${https.listener.idleTimeout}"
   protocol="HTTPS" tlsContext="TLS_Context_Inbound">
  </http:listener-connection>
 </http:listener-config>
```

## TLS_Context_Inbound

```xml
<tls:context name="TLS_Context_Inbound"
  doc:name="TLS Context" doc:id="90e84bb8-527e-4be0-9301-a0b9b78adc5c">
  <tls:key-store type="jks"
   path="${https.listener.keystore.path}"
   keyPassword="${secure::https.listener.keystore.keyPassword}"
   password="${secure::https.listener.keystore.password}" />
 </tls:context>
```

## TLS_Context_Transaction_DB_Outbound

```xml
 <tls:context name="TLS_Context_Transaction_DB_Outbound"
  doc:name="TLS Context" doc:id="a646a172-f12f-454f-967e-0ad7b6820451">
  <tls:trust-store
   path="${https.request.transactionDBSysApi.truststore.path}"
   password="${secure::https.request.transactionDBSysApi.truststore.password}"
   type="jks" />
 </tls:context>
```

## APIKit

```xml
<apikit:config name="3degreesArtemisSysAPI-config" 
 api="resource::a970b687-ceb1-48a0-9bc7-6fed0e331363:3degrees-artemis-sys-api:1.0.7:raml:zip:3degreesArtemisSysAPI.raml" outboundHeadersMapName="outboundHeaders" 
 httpStatusVarName="httpStatus" />
```

## Configuration Properties (config-common.properties)

```xml
 <configuration-properties doc:name="Configuration properties" 
  doc:id="ee8b5714-bcfb-4567-a880-9ae0e5798eb6" 
  file="config\config-common.properties" />
```

## Configuration Properties (config\config-${mule.env}.properties)

```xml
 <configuration-properties doc:name="Configuration properties"
  doc:id="86c521dc-6df6-4f33-8253-61fd6cef02d1"
  file="config\config-${mule.env}.properties" />
 ```

## Secure_Properties_Config

```xml
 <secure-properties:config name="Secure_Properties_Config" 
  doc:name="Secure Properties Config" 
  doc:id="06e02d7e-d356-42b6-90ba-b5fc456a5e04" 
  file="config\config-${mule.env}-secure.properties" 
  key="${mule.key}" >
  <secure-properties:encrypt algorithm="Blowfish" />
 </secure-properties:config>
 ```

## Configuration (global-error-handler)

```xml
 <configuration doc:name="Configuration" doc:id="8853fea1-ac72-48db-ab31-7ea3723e16ba" defaultErrorHandler-ref="global-error-handler" />
```

## HTTPS_Request_Transaction_DB_SYS_API

```xml
 <http:request-config
  name="HTTPS_Request_Transaction_DB_SYS_API"
  doc:name="HTTP Request configuration"
  doc:id="fcfb5c85-53a2-469f-8f2f-53fe23176d05">
 
  <http:request-connection
   host="${https.request.transactionDBSysApi.host}" protocol="HTTPS"
   port="${https.request.transactionDBSysApi.port}"
   connectionIdleTimeout="${https.request.transactionDBSysApi.connectionTimeout}">
   <reconnection>
    <reconnect
     frequency="${https.request.transactionDBSysApi.reconnection.frequency}"
     count="${https.request.transactionDBSysApi.reconnection.attempts}" />
   </reconnection>
   <tls:context >
    <tls:trust-store insecure="true" />
   </tls:context>
  </http:request-connection>
 
  <http:default-headers>
   <http:default-header key="client_id"
    value="#[p('secure::https.request.transactionDBSysApi.headers.clientId')]" />
   <http:default-header key="client_secret"
    value="#[p('secure::https.request.transactionDBSysApi.headers.clientSecret')]" />
  </http:default-headers>
 </http:request-config>
```

## HTTP_Request_UDA_Endpoint

```xml
 <http:request-config name="HTTP_Request_UDA_Endpoint" 
  doc:name="HTTP Request configuration" 
  doc:id="02196e9e-d382-4aba-908a-1a6e2ffe174c" 
  basePath="#[p('http.request.uda.basePath')]" 
  responseTimeout="#[p('http.request.uda.responseTimeout')]">
  <http:request-connection
   host="${http.request.uda.host}" 
   port="${http.request.uda.port}" 
   connectionIdleTimeout="${http.request.uda.connectionTimeout}" >
   <reconnection >
   </reconnection>
  </http:request-connection>
 </http:request-config>
 ```

## API Autodiscovery

 ```xml
 <api-gateway:autodiscovery apiId="${api.autodiscoveryId}"
  ignoreBasePath="true" 
  doc:name="API Autodiscovery" 
  doc:id="41a7eeb7-8cb3-4c84-a1ad-cf0af12f03be" 
  flowRef="3degreesArtemisSysAPI-main" />
```
