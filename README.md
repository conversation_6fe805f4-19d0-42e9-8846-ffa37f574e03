# Apisero_Artemis_SYS_API: feature branch

## INTERFACE

[interface](documentation/interface.md) API Router & Endpoints

## COMMON

[global](documentation/global.md) Global Configuration Elements  
[global-error-handler](documentation/global-error-handler.md) Global Error Handler  

## COMMON FLOWS

[sf-get-artemis-ids](documentation/sf-get-artemis-ids.md) Get Counterparty or Enterprise ID from `Enterprise_ID`   
[sf-insert-into-transaction-task-details-table](documentation/sf-insert-into-transaction-task-details-table.md) Insert Transaction in `Enterprise_ID`  
[sf-update-transaction-task-details-table](documentation/sf-update-transaction-task-details-table.md) Update Transaction in `Enterprise_ID` 

## IMPLEMENTATION

[pf-create-artemis-account](documentation/pf-create-artemis-account.md) Create Artemis Account (Counterparty)   
[pf-get-artemis-account](documentation/pf-get-artemis-account.md) Get Artemis Account (Counterparty)    
[pf-update-artemis-account](documentation/pf-update-artemis-account.md) Update Artemis Account (Counterparty)    
[pf-delete-artemis-account](documentation/pf-delete-artemis-account.md) Delete Artemis Account (Counterparty)    

---

## CONFIGURATION

[config](documentation/config.md) Configuration
