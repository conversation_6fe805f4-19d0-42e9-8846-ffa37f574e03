<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:java="http://www.mulesoft.org/schema/mule/java"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:xero-accounting="http://www.mulesoft.org/schema/mule/xero-accounting"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/xero-accounting http://www.mulesoft.org/schema/mule/xero-accounting/current/mule-xero-accounting.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/java http://www.mulesoft.org/schema/mule/java/current/mule-java.xsd">
	<flow name="pf-create-artemis-account"
		doc:id="27c58e04-36ee-43d2-a008-48ca7760fa36">
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="898f8464-77f0-4bc4-8937-d026013ec269"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-create-artemis-account", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="0194ee9c-8c0a-4a79-92cb-00d24fbe20a6"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-create-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="82a3a251-c90a-461f-b6ae-67908696d2d9"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-create-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vDbRecord, vRequestAttributes" doc:id="60e0c177-ea66-4a24-b208-d0f9be3c20d9" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
	"CORRELATION_ID": vars.vCorrelationId,
	"OBJECT_TYPE": "ACCOUNT",
	"OPERATION": "CREATE",
	"SOURCE": vars.vAttributes.headers.'x-source' default null,
	"TASK_PAYLOAD": write(payload, "application/json") default null,
	"DESTINATION": "ARTEMIS",
	"STATUS": "IN_PROGRESS",
	"RETRY_COUNT": 0,
	"LAST_UPDATED_BY": "SYSTEM_API",
	"QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
	"ERROR_MSG": null,
    "ERROR_TYPE": null
}]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "ARTEMIS_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<choice doc:name="Check header context" doc:id="df842adc-e65a-4708-98bd-0618442c6a39" >
			<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
				<flow-ref doc:name="Flow Reference to sf-insert-into-transaction-task-details-table" doc:id="21d20c52-6e66-45cc-9ec5-696fc68a1f9f" name="sf-insert-into-transaction-task-details-table" />
			</when>
			<when expression="#[((vars.vAttributes.headers.'context' ~= &quot;ROLLBACK&quot;) or (vars.vAttributes.headers.'context' ~= &quot;RETRY&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Skip STATUS update" doc:id="f704fc6d-939b-4b77-981c-2cce5f8662aa" message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;No update is made to TRANSACTION_TASK_DETAILS table as headers context is &quot; ++ (vars.vAttributes.headers.'context' default &quot;&quot;),&#10;	&quot;FlowName&quot; : &quot;pf-create-artemis-account&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId&#10;}]" />
			</when>
			<otherwise >
				<raise-error doc:name="CUSTOM:INVALID_HEADER_CONTEXT" doc:id="16002f64-6f3a-46ac-bf83-6730d94d4a3e" type="CUSTOM:INVALID_HEADER_CONTEXT" description="#[(&quot;Invalid value for context &quot; ++ (vars.vAttributes.headers.'context' default &quot;null&quot;) ++ &quot; is passed&quot;)]"/>
			</otherwise>
		</choice>
		<ee:transform
			doc:name="Set vCounterpartyRecord"
			doc:id="0aba7420-6f57-4d4a-9be4-1623997cfa8b">
			<ee:message>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vCounterpartyRecord" ><![CDATA[%dw 2.0
output application/json
---
[
    {
        "database": "Artemis",
        "schema": "port",
        "table": "tdt_Counterparty",
        "value": [
            {
				"CounterpartyName": payload.account.'counterpartyName',
				"ChangeUser": payload.account.'changeUser',
				"GreeneFlag": (payload.account.'greeneFlag' default "false"),
				"BusinessStreet": payload.account.'businessStreet',
                "BusinessStreet2": payload.account.'businessStreet2',
                "BusinessCity": payload.account.'businessCity',
                "BusinessState": payload.account.'businessState',
                "BusinessPostalCode": payload.account.'businessPostalCode',
                "BusinessCountry": payload.account.'businessCountry',
				"Enterprise_ID": payload.account.'eid' default ""
			}
		]
	}
]]]></ee:set-variable>
			</ee:variables>
		
</ee:transform>
		<try doc:name="Try" doc:id="dee06cd9-45c5-4b7b-a394-ee030fc4af39">
			<set-variable value='#["0" as Number]'
				doc:name="vRetryCount" doc:id="957faf96-1c43-4035-9ad5-7ca621326895"
				variableName="vRetryCount" />
			<until-successful maxRetries="${artemis.retry.maxRetries}"
				doc:name="Until Successful"
				doc:id="490b43e3-563b-4fc3-8188-cc9507a8dd51"
				millisBetweenRetries="${artemis.retry.timePeriod}">
				<try doc:name="Try"
					doc:id="fac71fda-49d8-43ec-9fc7-39809aec4360">
					<!-- <db:insert doc:name="Insert Counterparty record"
						doc:id="8cd294e7-4b8a-4e29-9eab-6b59011d7b05"
						config-ref="Database_Config_Transactions"
						target="vAddCounterpartyResponse">
						<db:sql><![CDATA[INSERT INTO tdt_Counterparty (CounterpartyID, CounterpartyName, ChangeUser, GreeneFlag) VALUES (:counterpartyId, :counterpartyName, :changeUser, :greenFlag);]]></db:sql>
						<db:input-parameters><![CDATA[#[{
	"counterpartyId": vars.vCounterpartyRecord.counterpartyId default -1,
	"counterpartyName": vars.vCounterpartyRecord.counterpartyName default "",
	// "enterpriseId": vars.vCounterpartyRecord.enterpriseId default "",
	"changeUser": vars.vCounterpartyRecord.changeUser default "",
	"greenFlag": vars.vCounterpartyRecord.greeneFlag default false,
}]]]></db:input-parameters>
					</db:insert> -->
					<http:request method="POST" doc:name="Create Counterparty record in Artemis" doc:id="b71f5fb8-d35f-42f1-80b2-f02aa27b8163" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.postDatabase') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.postSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.insertObjectName')]" target="vAddCounterpartyResponse">
						<http:body ><![CDATA[#[vars.vCounterpartyRecord]]]></http:body>
					</http:request>
					
					<set-variable value='#[output application/json&#10;var addResponse = read(vars.vAddCounterpartyResponse, "application/json")&#10;---&#10;addResponse.Table[0].Column1 ~= "Commands completed successfully."]' doc:name="vCreationFlag" doc:id="33f98728-6d5b-4544-b827-7501883729ee" variableName="vCreationFlag"/>
					<choice doc:name="Check if Artemis insertion is succssful" doc:id="6a0920bb-d948-4f5e-9006-fbc28fd542b9">
				<when expression="#[vars.vCreationFlag]">
					<java:invoke-static method="sleepThread(String)" doc:name="sleepThread(String)" doc:id="da663d58-562c-4001-81b7-0ae11574fd25" class="com.javaUtilities.SleepThread" target="vSleepThreadResponse">
						<java:args><![CDATA[#[arg0: Mule::p('artemis.sleepThread.Timeperiod')]]]></java:args>
					</java:invoke-static>
					<http:request method="GET" doc:name="Fetch Counterparty record from Artemis" doc:id="895abc51-e839-4246-b6b4-9c8eba1e03cc" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.readDatabase') ++ &quot;&amp;objectType=&quot; ++ p('https.request.uda.endpoint.objectType') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.readSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.readObjectName') ++ &quot;&amp;filters=where CounterpartyName = '&quot; ++ ((payload.account.'counterpartyName' default &quot;&quot;) replace &quot;'&quot; with &quot;''&quot;) ++ &quot;'&quot;]" target="vRetrieveCounterpartyResponse" />
				</when>
				<otherwise>
					<logger level="WARN" doc:name="LOG WARN: Creation failed" doc:id="03b1e95c-513f-4e33-aae7-d55798b9a21c" message="#[var addResponse = read(vars.vAddCounterpartyResponse, &quot;application/json&quot;)&#10;output application/json&#10;---&#10;{ 	&#10;	&quot;Message&quot;: &quot;Log Outbound Request&quot;,&#10;	&quot;FlowName&quot;: &quot;pf-create-artemis-account&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId,&#10;	&quot;message&quot;: &quot;Error occured while inserting record with CounterpartyName: &quot; ++ (payload.account.'counterpartyName' default &quot;&quot;),&#10;	&quot;description&quot;: (addResponse.Table[0].Column1 default addResponse.Table[0].Error) default &quot;&quot;&#10;}]" />
				</otherwise>
			</choice>
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="94ee43b6-6d6f-4f22-b653-75cb4bce69f3" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
var addResponse = read(vars.vAddCounterpartyResponse, "application/json")
output application/json
---
if(vars.vCreationFlag) (
	vars.vDbRecord update {
		case STATUS at .STATUS ->  "COMPLETED"
	    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
	}
)
else (
	vars.vDbRecord update {
		case STATUS at .STATUS -> "FAILED"
	    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
	    case ERROR_MSG at .ERROR_MSG -> ((addResponse.Table[0].Column1 default addResponse.Table[0].Error) default "") replace "\"" with ""
	    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
	}
)]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					
					<error-handler>
						<on-error-propagate enableNotifications="true"
							logException="true" doc:name="On Error Propagate"
							doc:id="95223511-fb09-4c74-b0c6-5ed657abe8fd"
							type="HTTP:CONNECTIVITY, HTTP:TIMEOUT">
							<set-variable
								value="#[(vars.vRetryCount as Number) + 1]"
								doc:name="Update vRetryCount"
								doc:id="cc8c6245-b433-4417-9ad1-e758216d7911"
								variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="908cc851-7aa8-4565-8213-a02cc6db9278"
							type="HTTP:BAD_REQUEST, HTTP:CLIENT_SECURITY, HTTP:FORBIDDEN, HTTP:UNAUTHORIZED">
							
							<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="cbf4770d-0968-468a-b54e-2f778acea284" >
								<ee:message >
								</ee:message>
								<ee:variables >
									<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
}]]></ee:set-variable>
									<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<flow-ref
				doc:name="Flow Reference to sf-update-transaction-task-details-table"
				doc:id="ff6c32d5-028b-4cf9-a09d-fff23d112355"
				name="sf-update-transaction-task-details-table" />
			<ee:transform doc:name="Set payload, httpStatus" doc:id="aa9caca4-1e88-455d-bf32-1e9a5d95ced6" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
import * from dw::Runtime
import * from dw::core::Strings
output application/json
---
if((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vCreationFlag)) do {
	var retrieveRecord = read(vars.vRetrieveCounterpartyResponse, "application/json")
	--- 
	{
	  "code": 201,
	  "transactionId": vars.vTransactionId,
	  "status": "SUCCESS",
	  "response": do {
	  	var counterpartyId = if(isEmpty(retrieveRecord.'Table'[0].'CounterpartyID')) do {
			var logger = log("WARNING", "Could not retrieve CounterpartyID after successful Account creation. Record: " ++ (payload.account.'counterpartyName' default "") ++ " | EnterpriseID: " ++ (payload.account.'eid' default ""))
			---
			null
		} else retrieveRecord.'Table'[0].'CounterpartyID'
	  	---
	  	{
		    "counterpartyID": counterpartyId,
		    "eid": payload.account.'eid' default ""
		}
	  }
	}
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDBRecord.'ERROR_TYPE',
      "details": vars.vDBRecord.'ERROR_MSG'
    }
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/json
---
if(vars.vCreationFlag default false) 201 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true"
					logException="true" doc:name="On Error Propagate"
					doc:id="3eefd7a7-34ec-44cc-923c-41c89fbef3ea" type="ANY">
						
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="35a79f9a-1867-4818-9d51-4e1cf9581af7" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
    case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> p('artemis.retry.maxRetries') as Number
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "SYSTEM"
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					
					<flow-ref
						doc:name="Flow Reference to sf-update-transaction-task-details-table"
						doc:id="2727a857-8331-4159-bbd9-f6f4176d212e"
						name="sf-update-transaction-task-details-table" />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="042f9b60-d0f1-4395-8a6f-ab1bf10be813"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-create-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="f15f45b8-d28e-4f13-8d94-************"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-create-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="********-744c-4c94-94c7-f7474c7da21d"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-create-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
