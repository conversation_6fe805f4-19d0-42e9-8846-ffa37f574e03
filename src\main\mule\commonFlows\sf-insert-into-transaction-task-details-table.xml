<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd">
	<sub-flow
		name="sf-insert-into-transaction-task-details-table"
		doc:id="4ddf4d95-0151-4c5b-9c69-fc7ace880f03">
		
		<http:request method="POST" doc:name="Post record into TRANSACTION_TASK_DETAILS" doc:id="db078863-291f-46bb-9bd9-e5a34dcafc89" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/TRANSACTION_TASK" target="vInsertDBRecordResponse">
			<http:body ><![CDATA[#[output application/json
---
{
	"transactionTask": vars.vDbRecord
}]]]></http:body>
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
		</http:request>
		
		<!-- <db:insert
			doc:name="Insert into TRANSACTION_TASK_DETAILS table"
			doc:id="f08868d0-2513-4bbd-b029-1f01b6df9324"
			config-ref="Database_Config_Transactions"
			target="vInsertDBRecordResponse">
			<db:sql><![CDATA[INSERT INTO TRANSACTION_TASK_DETAILS (CREATED_DATE, OPERATION, CORRELATION_ID, TASK, TRANSACTION_PAYLOAD, DESTINATION, STATUS, RETRY_COUNT, LAST_UPDATED_BY, MAX_RETRY, QUERY_PARAMS, ERROR_MSG, ERROR_TYPE) VALUES (:createdDate, :operation, :correlationId, :task, :transactionPayload, :destination, :status, :retryCount, :lastUpdatedBy, :maxRetry, :queryParams, :errorMessage, :errorType)]]></db:sql>
			<db:input-parameters><![CDATA[#[{
	"createdDate": vars.vDbRecord.'createdDate',
	"operation": vars.vDbRecord.'operation',
	"correlationId": vars.vDbRecord.'correlationId',
	"task": vars.vDbRecord.'task',
	"transactionPayload": write(vars.vDbRecord.'transactionPayload', "application/json") as String,
	"destination": vars.vDbRecord.'destination',
	"status": vars.vDbRecord.'status',
	"retryCount": vars.vDbRecord.'retryCount',
	"lastUpdatedBy": vars.vDbRecord.'lastUpdatedBy',
	"maxRetry": vars.vDbRecord.'maxRetry',
	"queryParams": vars.vDbRecord.'queryParams',
	"errorMessage": vars.vDbRecord.'errorMessage',
	"errorType": vars.vDbRecord.'errorType'
}]]]></db:input-parameters>
		</db:insert> -->
	</sub-flow>
</mule>
