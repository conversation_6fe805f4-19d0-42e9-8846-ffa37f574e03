<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:xero-accounting="http://www.mulesoft.org/schema/mule/xero-accounting" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/xero-accounting http://www.mulesoft.org/schema/mule/xero-accounting/current/mule-xero-accounting.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-update-artemis-record-ids" doc:id="dc9ba242-b469-435a-b432-4c92dfc21f38" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="1ae1b59e-953f-48b2-add3-227333227be7"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-update-artemis-record-ids", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="26bcd9e8-0f74-4e07-880e-7f0076e7c45f"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"BusinessKey": vars.vBusinessKey&#10;	&#10;	}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="80957ec6-a6d4-403b-a9f1-411125e526dc"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<!-- <ee:transform doc:name="Convert payload to string" doc:id="ecdcb9b6-fa19-4d27-a24a-25539a63291e" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vPayload" ><![CDATA[%dw 2.0
output text/plain
import * from dw::core::URL
-&#45;&#45;
encodeURI(write(vars.vUpdateRecordIDsBody.payload, "application/json") as String) 
]]></ee:set-variable>
			</ee:variables>
		</ee:transform> -->
		<try doc:name="Try" doc:id="ced17c8a-0123-4b87-a153-a42726d01ddb" >
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="bd806d58-b6cd-4b99-aa7b-e5b4ccba7b20" variableName="vRetryCount" />
			<until-successful maxRetries="${artemis.retry.maxRetries}" doc:name="Until Successful" doc:id="eabed5e5-9bf6-4f79-8f35-708609e42d68" millisBetweenRetries="${artemis.retry.timePeriod}">
				<try doc:name="Try" doc:id="861904ca-64c3-44d8-a0df-1fe10357eb20">
					
					<http:request method="POST" doc:name="Update record IDs  in Artemis" doc:id="5cd57f3c-cccc-4c55-aaa8-bc70e209a1af" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.readDatabase') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.submitRequestSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.submitRequestObjectName')]" target="vUpdateRecordIDsResponse">
					</http:request>
					
					<logger level="DEBUG" doc:name="LOG DEBUG: Log UDA Raw Response" doc:id="788f881e-8274-4b23-9c4c-1ed6ceb4f52a" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log UDA Raw Response",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"Raw Response": read(vars.vUpdateRecordIDsResponse, "application/json"),&#10;	"BusinessKey": vars.vBusinessKey&#10;}]'/>
					<set-variable value='#[output application/json&#10;var updateResponse = read(vars.vUpdateRecordIDsResponse, "application/json")&#10;---&#10;updateResponse.syncStatus ~= "SUCCESS"]' doc:name="vUpdateFlag" doc:id="c9847068-7f78-4a28-97c9-40d651ce50f8" variableName="vUpdateFlag"/>
					
					<choice doc:name="Check if sync status is success" doc:id="3519b092-bdef-4655-a47b-cb541479e483" >
						<when expression="#[!(vars.vUpdateFlag)]">
							<logger level="WARN" doc:name="LOG WARN: Update failure" doc:id="ec287dae-8dad-4464-8fca-cad7f155d7ee" message='#[%dw 2.0&#10;var syncDetails = read(vars.vUpdateRecordIDsResponse default "", "application/json")&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log UDA Response",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"BusinessKey": vars.vBusinessKey,&#10;	"ErrorDescription": (&#10;		if((syncDetails.syncStatus ~= null) and (syncDetails.syncMessage ~= null)) (&#10;			if((syncDetails.Table1[0].Source != null) or (syncDetails.Table1[0].Error != null)) ((syncDetails.Table1[0].Source default "") ++ " | " ++ (syncDetails.Table1[0].Error default ""))&#10;			else "Something went wrong with UDA endpoint"&#10;		)&#10;		else (syncDetails.syncStatus default "") ++ " | " ++ (syncDetails.syncMessage default "") &#10;	)&#10;	&#10;}]' />
						</when>
						<otherwise >
							<logger level="INFO" doc:name="LOG INFO: Update Success" doc:id="1503325b-2a99-4b0b-8e1f-56f459dba44b" message='#[%dw 2.0&#10;var syncDetails = read(vars.vUpdateRecordIDsResponse default "", "application/json")&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log UDA Response",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"BusinessKey": vars.vBusinessKey,&#10;	"SuccessResponse": (syncDetails.syncStatus default "") ++ " | " ++ (syncDetails.syncMessage default "") &#10;	&#10;}]'/>
						</otherwise>
					</choice>
					<error-handler>
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="0186f449-5e86-4a3f-b8dc-ada283e83326" type="HTTP:CONNECTIVITY, HTTP:TIMEOUT">
							<logger level="ERROR" doc:name="LOG ERROR: Log Error Description" doc:id="55406bf9-4532-43bc-9d3e-7f6cd474e7be" message='#[output application/json&#10;---&#10;{ 	&#10;	"Message" : "Error occured while updating record IDs in Artemis",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"ErrorDescription": error.description,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="ed0211ba-dcb5-47fb-b0b1-ef209977cdd1" type="HTTP:BAD_REQUEST, HTTP:CLIENT_SECURITY, HTTP:FORBIDDEN, HTTP:UNAUTHORIZED">
							<ee:transform doc:name="Update vUpdateFlag, vUpdateRecordIDsResponse" doc:id="03f34ac5-2e87-4c71-b043-5eb1b853e2d0" >
								<ee:message />
								<ee:variables >
									<ee:set-variable variableName="vUpdateRecordIDsResponse" ><![CDATA[output application/json
---
{
	"Error": error.errorType,
	"ErrorDescription": error.description
}
		
	
	
]]></ee:set-variable>
									<ee:set-variable variableName="vUpdateFlag" ><![CDATA[false]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<error-handler >
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="887c8e8b-00b8-49bc-8824-f21946685ecc" type="ANY">
					<logger level="ERROR" doc:name="LOG ERROR: Log Error Description" doc:id="74c170e5-4f2e-4f13-8711-48970a48c654" message='#[output application/json&#10;---&#10;{ 	&#10;	"Message" : "Error occured while updating record IDs in Artemis",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"ErrorDescription": error.description,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
				</on-error-propagate>
			</error-handler>
		</try>
		<ee:transform doc:name="Set Final Response and httpStatus" doc:id="f3296dca-b3d5-4cc1-98c5-492b126b88ee">
				<ee:message>
					<ee:set-payload><![CDATA[output application/json 
var syncDetails = read(vars.vUpdateRecordIDsResponse default "", "application/json")
---
{
  "code": if(vars.vUpdateFlag ) 200 else 400,
  "transactionId": syncDetails.transactionID,
  "previousTransactionId": syncDetails.previousTransactionID,
  "status": if(vars.vUpdateFlag)"SUCCESS" else "FAILURE",
  "response": (
        if(vars.vUpdateFlag) {
            "message": syncDetails.syncMessage,
            "result": syncDetails.syncStatus,
            "updatedBy": syncDetails.updatedBy
        } else (
            if((syncDetails.syncStatus ~= null) and (syncDetails.syncMessage ~= null)) ( // not an error from SP
                if((syncDetails.Table1[0].Source != null) or (syncDetails.Table1[0].Error != null)) { // error from server
                    "message": syncDetails.Table1[0].Source,
                    "details": syncDetails.Table1[0].Error
                } else { // some internal error of Artemis
                    "message": "Error occurred while submitting request to update recordIDs in Artemis",
                    "details": "Something went wrong"
                }
            ) else { // some valid (data validation) error
                "message": syncDetails.syncMessage,
                "details": syncDetails.syncStatus
            }
        )
    ) 
}]]></ee:set-payload>
				</ee:message>
				<ee:variables>
					<ee:set-variable variableName="httpStatus"><![CDATA[%dw 2.0
output application/json
---
if(vars.vUpdateFlag default false) 200 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="f989ed4f-063b-4022-9946-b832355b6da3"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-update-artemis-record-ids",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="51023098-1e81-4ce0-87a3-d01040b3564f"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-update-artemis-record-ids",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/recordIDs",&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="c0bdf248-988e-4ff7-ba24-f6fee853d799"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-update-artemis-record-ids",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey": vars.vBusinessKey&#10;}]' />
	</flow>
</mule>
