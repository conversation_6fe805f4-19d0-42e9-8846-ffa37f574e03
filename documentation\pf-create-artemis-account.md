# pf-create-artemis-account

## Flow: pf-create-artemis-account

## Start Logging

- LOG INFO: Log Entry
- LOG INFO: Log Outbound Request
- LOG DEBUG: Log Outbound Request Payload

## Variable: vRequestAttributes

```json
{
 "headers": {
  "x-source": vars.vAttributes.headers.'x-source' default "",
  "x-transactionId": vars.vTransactionId,
  "x-msg-timestamp": now() as DateTime,
  "correlationId": vars.vCorrelationId,
  "sourceId": "ARTEMIS_SYS_API",
  "destinationId": "TRANSACTION_DB_SYS_API",
  "content-type": "application/json"
 }
}
```

## Choice: Check Header Context

```mermaid
---
title: Check Header Context
---
flowchart LR
    A{Choice: Check header context}
    A{vars.vAttributes.headers.'context'}
    A --> B[REGULAR]
    A --> C[ROLLBACK or RETRY]
    A --> D[CUSTOM:INVALID_HEADER_CONTEXT]
    B --> B1[[sf-insert-into-transaction-task-details-table]]  
    C --> C1[LOG INFO: Skip STATUS update]
    D --> D1["Invalid value for context is passed"]
```

## Transform Message: Set vCounterpartyRecord

```json
// Artemis API: Create Counterparty Payload
[
    {
        "database": "Artemis",
        "schema": "port",
        "table": "tdt_Counterparty",
        "value": [
            {
                "CounterpartyName": payload.account.'counterpartyName',
                "ChangeUser": payload.account.'changeUser',
                "GreeneFlag": (payload.account.'greeneFlag' default "false"),
                "BusinessStreet": payload.account.'businessStreet',
                "BusinessStreet2": payload.account.'businessStreet2',
                "BusinessCity": payload.account.'businessCity',
                "BusinessState": payload.account.'businessStreet',
                "BusinessPostalCode": payload.account.'businessPostalCode',
                "BusinessCountry": payload.account.'businessCountry'
   }
  ]
 }
]
```

## HTTP Request: Create Counterparty Record (Artemis API)

- Body: `vCounterpartyRecord`
- Target Variable: `vAddCounterpartyResponse`

## Variable: vCreation Flag

> creates variable using the `vAddCounterpartyResponse` variable created when record posted to Artemis

```js
var addResponse = read(vars.vAddCounterpartyResponse, 
"application/json")
---
addResponse.Table[0].Column1 ~= "Commands completed successfully."
```

## Choice: Check if Artemis Insertion is Successful

```mermaid
flowchart LR
    A{Choice: Check header context}
    A --> B[Exists]
    A --> C[Not Exists]
    B --> B1[(Artemis: Get Counterparty)]  
    C --> C1[LOG WARN: Creation failed]
```

## Variable: vRequestAttributes Update

```json
vars.vRequestAttributes update {
 case timestamp at .headers.'x-msg-timestamp' -> now()
}
```

## Flow Reference: sf-update-transaction-task-details-table

[sf-update-transaction-task-details-table](sf-update-transaction-task-details-table.md)

## Transform Message: Set Payload

```json

if((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vCreationFlag)) do {
 var retrieveRecord = read(vars.vRetrieveCounterpartyResponse, "application/json")
 --- 
 {
   "code": 201,
   "transactionId": vars.vTransactionId,
   "status": "SUCCESS",
   "response": {
     "counterpartyID": retrieveRecord.'Table'[0].'CounterpartyID' default "",
     "eid": payload.account.'eid' default ""
   }
 }
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDBRecord.'ERROR_TYPE',
      "details": vars.vDBRecord.'ERROR_MSG'
    }
}
```

## End Logging

- LOG DEBUG: Log Outbound Response Payload
- LOG INFO: Log Outbound Response
- LOG INFO: Log Exit
