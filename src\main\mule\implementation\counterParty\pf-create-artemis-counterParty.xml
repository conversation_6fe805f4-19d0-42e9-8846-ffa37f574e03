<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd 
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<flow name="pf-create-artemis-counterParty"
		doc:id="9a0f27ec-4014-43c4-97a3-940cb683a4b6">
		<set-variable
			value="#[attributes.headers.'x-businessKey' default &quot;&quot;]"
			doc:name="businessKey" doc:id="acb5cd07-a27c-4cef-8702-bb0516286409"
			variableName="businessKey" />
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="747174a7-f3ec-4b14-8a41-b35d6075b420"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-create-artemis-counterParty-flow", &#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="82f3dfd4-f1fa-49df-97e8-f7f3a12bb0ca"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="d5ba191d-ed22-4f53-807e-e189eeb4c17b"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"BackendRequest": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<ee:transform doc:name="Set vUpdateSyncStatusesBody"
			doc:id="d6cee2cb-ee48-4204-9650-97ec905bb6e5">
			<ee:message />
			<ee:variables>
				<ee:set-variable
					variableName="vUpdateSyncStatusesBody"><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
payload]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<try doc:name="Try" doc:id="ef300ed3-afee-4346-8df1-3f15791d48ac">
			<set-variable value='#["0" as Number]'
				doc:name="vRetryCount" doc:id="65451f57-04ad-45ee-80ad-0fe06537b3d8"
				variableName="vRetryCount" />
			<until-successful
				maxRetries="${artemis.retry.maxRetries}" doc:name="Until Successful"
				doc:id="f61ff01a-8fca-4081-b986-4f0c06f3940d"
				millisBetweenRetries="${artemis.retry.timePeriod}">
				<try doc:name="Try"
					doc:id="6dedf026-25bd-48bc-b77b-1b08d6f674da">

					<http:request method="POST"
						doc:name="Create CounterParty in Artemis"
						doc:id="3745bcce-6199-4898-89a2-3afdaf3c16be"
						config-ref="HTTPS_Request_UDA_Endpoint"
						path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.readDatabase') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.submitRequestSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.submitRequestObjectName')]"
						target="vCreateCounterPartyResponse">
						<http:body><![CDATA[#[vars.vUpdateSyncStatusesBody]]]></http:body>
					</http:request>

					<logger level="DEBUG"
						doc:name="LOG DEBUG: Log UDA Raw Response"
						doc:id="a360721e-9379-4934-ae80-9d144dca7678"
						message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log UDA Raw Response",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"Raw Response": read(vars.vCreateCounterPartyResponse, "application/json"),&#10;	"BusinessKey": vars.businessKey&#10;}]' />
					<set-variable
						value='#[output application/json&#10;var updateResponse = read(vars.vCreateCounterPartyResponse, "application/json")&#10;---&#10;updateResponse.syncStatus ~= "SUCCESS"]'
						doc:name="vCreateFlag"
						doc:id="d41cd0e3-4d21-4b0b-b63d-7e1844bda832"
						variableName="vCreateFlag" />

					<choice doc:name="Check if sync status is success"
						doc:id="f5c2a76b-49ba-439f-aad1-bdd8d0be8ecc">
						<when expression="#[vars.vCreateFlag]">
							<logger level="INFO" doc:name="LOG INFO: Excecution completed" doc:id="28cda477-280d-496c-9fe2-78f509fc91b1" message='#[%dw 2.0&#10;var details = read(vars.vCreateCounterPartyResponse default "", "application/json")&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Excecution completed",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"SuccessResponse": (details.syncStatus default "") ++ " | " ++ (details.syncMessage default "")&#10;}]'/>
						</when>
						<otherwise >
							<logger level="WARN" doc:name="LOG WARN: Exception occurred while execution" doc:id="3e1f2ba0-cd07-479a-8701-1301c12c65c6" message='#[%dw 2.0&#10;var details = read(vars.vCreateCounterPartyResponse default "", "application/json")&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Exception occurred with UDA endpoint execution",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"ErrorDescription": (&#10;		if((details.syncStatus ~= null) and (details.syncMessage ~= null)) (&#10;			if((details.Table1[0].Source != null) or (details.Table1[0].Error != null)) ((details.Table1[0].Source default "") ++ " | " ++ (details.Table1[0].Error default ""))&#10;			else "Something went wrong with UDA endpoint"&#10;		)&#10;		else (details.syncStatus default "") ++ " | " ++ (details.syncMessage default "") &#10;	)&#10;}]' />
						</otherwise>
					</choice>
					<error-handler>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="5e345c01-b777-42b3-9342-a959f4ca5bf2"
							type="HTTP:BAD_REQUEST">
							<ee:transform
								doc:name="Update vCreateFlag, vhttpStatus"
								doc:id="3c59a56d-dd97-4581-a026-3ee31a5c5c0f">
								<ee:message />
								<ee:variables>
									<ee:set-variable variableName="vCreateFlag"><![CDATA[false]]></ee:set-variable>
									<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="0c6eb90c-9ffc-4cf5-b6c8-50672af9f956"
							type="HTTP:NOT_FOUND">
							<ee:transform
								doc:name="Update vCreateFlag, vhttpStatus"
								doc:id="78960180-c970-4899-8c14-356369a9c661">
								<ee:message />
								<ee:variables>
									<ee:set-variable variableName="vCreateFlag"><![CDATA[false]]></ee:set-variable>
									<ee:set-variable variableName="httpStatus" ><![CDATA[404]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
						<on-error-continue enableNotifications="true"
							logException="true" doc:name="On Error Continue"
							doc:id="b52a7a3e-5b27-49f0-89fc-e1843d0481d0"
							type="ANY">
							<ee:transform
								doc:name="Update vCreateFlag, vhttpStatus"
								doc:id="7fc6370b-563b-4a50-af34-ce34feb29c99">
								<ee:message />
								<ee:variables>
									<ee:set-variable variableName="vCreateFlag"><![CDATA[false]]></ee:set-variable>
									<ee:set-variable variableName="httpStatus" ><![CDATA[500]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
							<logger level="INFO" doc:name="Logger" doc:id="a766d9bd-52f1-4a35-aaf1-1a52bf62fa1f" />
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<error-handler>
				<on-error-propagate enableNotifications="true"
					logException="true" doc:name="On Error Propagate"
					doc:id="fd239fb9-f5c1-4a6b-893c-9e048ef8676b" type="ANY">
					<logger level="ERROR"
						doc:name="LOG ERROR: Log Error Description"
						doc:id="cbc690eb-e288-4f0b-9f69-a8b34a59f5ad"
						message='#[output application/json&#10;---&#10;{ 	&#10;	"Message" : "Error occurred while submitting request to create/update counterParty",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"ErrorDescription": error.description,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
				</on-error-propagate>
			</error-handler>
		</try>
		<ee:transform
			doc:name="Set payload, httpStatus"
			doc:id="d48e1932-3bb3-4ae8-ac63-666cf04163a7">
			<ee:message>
				<ee:set-payload><![CDATA[output application/json 
var details = if(!isEmpty(vars.vCreateCounterPartyResponse))(read(vars.vCreateCounterPartyResponse default "", "application/json")) else null
---
{
  "code": if(vars.vCreateFlag) 201 else (vars.httpStatus default 500),
  "transactionId": vars.vTransactionId ,
  "status": if(vars.vCreateFlag) "SUCCESS" else "FAILURE",
  "response": (
        if(vars.vCreateFlag) {
            "accountId": details.payload.recordIDs[0].recordID,
            "eid": details.payload.recordIDs[0].universalID
        } else (
            if((details.syncStatus ~= null) and (details.syncMessage ~= null)) ( // not an error from SP
                if((details.Table1[0].Source != null) or (details.Table1[0].Error != null)) { // error from server
                    "message": details.Table1[0].source,
                    "details": details.Table1[0].Error
                } else { // some internal error of Artemis
                    "message": "Error occurred while submitting request to create/update counterParty in Artemis",
                    "details": error.description
                }
            ) else { // some valid (data validation) error
                "message": details.syncMessage,
                "details": details.syncStatus
            }
        )
    )
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus"><![CDATA[%dw 2.0
output application/json
---
if(vars.vCreateFlag) 201 else (vars.httpStatus default 500)]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="0b5e6713-8968-43d6-a458-07b913028a22"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-create-artemis-counterParty",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"BackendResponse": payload,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="b0e13f52-d059-43e5-b635-21dd9ec07144"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-create-artemis-counterParty",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/counterParty",&#10;	"BusinessKey": vars.businessKey&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="28a635e5-096b-4bd6-b415-c473f7445926"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-create-artemis-counterParty",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"BusinessKey": vars.businessKey&#10;}]' />
	</flow>
</mule>
