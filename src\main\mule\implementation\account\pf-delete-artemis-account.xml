<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:xero-accounting="http://www.mulesoft.org/schema/mule/xero-accounting" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/xero-accounting http://www.mulesoft.org/schema/mule/xero-accounting/current/mule-xero-accounting.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-delete-artemis-account" doc:id="cb164f07-9b9b-4f30-8136-d5445556c412" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="b4068bf0-9c2b-4ec2-996a-a1ad87b12f07"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-delete-artemis-account", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="6f9c046b-3dbd-4279-80f5-73c17406f1b6"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-delete-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="232f95bc-3c12-40f8-a49d-9a1868175b35"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-delete-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vDbRecord, vRequestAttributes" doc:id="78180e01-178a-4752-9400-093191ff0b81" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
	"CORRELATION_ID": vars.vCorrelationId,
	"OBJECT_TYPE": "ACCOUNT",
	"OPERATION": "DELETE",
	"SOURCE": vars.vAttributes.headers.'x-source' default null,
	"TASK_PAYLOAD": write(payload, "application/json") default null,
	"DESTINATION": "ARTEMIS",
	"STATUS": "IN_PROGRESS",
	"RETRY_COUNT": 0,
	"LAST_UPDATED_BY": "SYSTEM_API",
	"QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
	"ERROR_MSG": null,
    "ERROR_TYPE": null
}]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "ARTEMIS_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vCounterpartyId, vEnterpriseId" doc:id="403fa95b-82c2-48af-be2d-70d16507531e" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vCounterpartyId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'counterpartyID' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vEnterpriseId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
			

</ee:variables>
		</ee:transform>
		<choice doc:name="Check header context" doc:id="349cf0bf-b544-40e1-8b1d-86dc7aa4ae7f">
			<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
				<flow-ref doc:name="Flow Reference to sf-insert-into-transaction-task-details-table" doc:id="aba33f0c-e414-4594-88c7-de8c346c1aa3" name="sf-insert-into-transaction-task-details-table" />
			</when>
			<when expression="#[((vars.vAttributes.headers.'context' ~= &quot;ROLLBACK&quot;) or (vars.vAttributes.headers.'context' ~= &quot;RETRY&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Skip STATUS update" doc:id="dfce7dfb-9945-4e5b-a935-01f88a4e2e4e" message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;No update is made to TRANSACTION_TASK_DETAILS table as headers context is &quot; ++ (vars.vAttributes.headers.'context' default &quot;&quot;),&#10;	&quot;FlowName&quot; : &quot;pf-delete-artemis-account&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId&#10;}]" />
			</when>
			<otherwise>
				<raise-error doc:name="CUSTOM:INVALID_HEADER_CONTEXT" doc:id="b70cc5a4-79a2-4d71-895d-e14301bc93bc" type="CUSTOM:INVALID_HEADER_CONTEXT" description="#[(&quot;Invalid value for context &quot; ++ (vars.vAttributes.headers.'context' default &quot;null&quot;) ++ &quot; is passed&quot;)]" />
			</otherwise>
		</choice>
		<choice doc:name="Check if vInternalId is empty" doc:id="1db9b6b1-a002-4260-af30-4b811e7304d5" >
			<when expression="#[isEmpty(vars.vCounterpartyId)]">
				<flow-ref doc:name="Flow Reference to sf-get-artemis-id" doc:id="81666c5f-cb8d-48cb-b7da-598849ad986f" name="sf-get-artemis-id" target="vCounterpartyId"/>
			</when>
			<otherwise >
				<flow-ref doc:name="Flow Reference to sf-get-artemis-enterprise-id" doc:id="c565dfbf-1329-4930-9839-f3902a0b3688" name="sf-get-artemis-enterprise-id" target="vEnterpriseId" />
			</otherwise>
		
</choice>
		<!-- <db:select doc:name="Retrieve from Artemis" doc:id="6b5e2c89-db33-42a0-a348-6e5986593488" config-ref="Database_Config_Transactions" target="vRetrieveCounterpartyResponse">
			<db:sql ><![CDATA[SELECT * FROM tdt_Counterparty WHERE CounterpartyID = :counterpartyId]]></db:sql>
			<db:input-parameters ><![CDATA[#[{
	"counterpartyId": vars.vCounterpartyId
}]]]></db:input-parameters>
		</db:select> -->
		<ee:transform doc:name="Set vDeleteCounterpartyBody" doc:id="f9015a39-8d60-4de2-aed7-04cce85153c8">
					<ee:message>
					</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vDeleteCounterpartyBody" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
[
    {
        "database": "Artemis",
        "schema": "port",
        "table": "tdt_Counterparty",
        "whereclause": "CounterpartyID = '" ++ vars.vCounterpartyId ++ "'"
    }
]]]></ee:set-variable>
			
</ee:variables>
				
</ee:transform>
		
		<try doc:name="Try" doc:id="290cefcc-fe96-4a9d-b116-49ebc8585f09">
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="7045cf0b-3755-446d-9513-fce13b54ea97" variableName="vRetryCount" />
			<until-successful maxRetries="${artemis.retry.maxRetries}" doc:name="Until Successful" doc:id="9a9b5a2d-92ab-4b48-888e-e104c8d553b8" millisBetweenRetries="${artemis.retry.timePeriod}">
				<try doc:name="Try" doc:id="0b7ffca4-1cc0-451b-84c9-662ba52bc9f2">
					<http:request method="POST" doc:name="Delete Counterparty record from Artemis" doc:id="90bda8e3-668c-49c4-bcfd-363b6d1db9f7" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.postDatabase') ++ &quot;&amp;objectType=&quot; ++ p('https.request.uda.endpoint.objectType') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.postSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.deleteObjectName') ++ &quot;&amp;filters=where CounterpartyID = '&quot; ++ vars.vCounterpartyId ++ &quot;'&quot;]" target="vDeleteCounterpartyResponse">
			<http:body><![CDATA[#[vars.vDeleteCounterpartyBody]]]></http:body>
		
</http:request>
					<set-variable value='#[output application/json&#10;var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")&#10;var deleteFlag = deleteResponse.Table[0].Column1 ~= "Commands completed successfully."&#10;---&#10;(deleteFlag default false)]' doc:name="vDeletionFlag" doc:id="d44c7b54-1ecf-48fe-a551-396316f91419" variableName="vDeletionFlag"/>
					<choice doc:name="Choice" doc:id="ee96facf-909c-446d-bd42-cb2461804136" >
						<when expression='#[!(vars.vDeletionFlag)]'>
							<logger level="INFO" doc:name="LOG WARN: Deletion failure" doc:id="3d5529e1-ceeb-4a15-8807-844137ff038b" message='#[var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")&#10;output application/json&#10;---&#10;{ 	&#10;	"Message": "Log Outbound Request",&#10;	"FlowName": "pf-delete-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"message": "Error occured while deleting record with CounterpartyId: " ++ (vars.vCounterpartyId default ""),&#10;	"description": (deleteResponse.Table[0].Column1 default deleteResponse.Table[0].Error) default ""&#10;}]'/>
						</when>
					</choice>
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="1170c797-bd22-4c0a-beac-3d43619c28ca">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="vDbRecord"><![CDATA[output application/json
var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")
---
if(vars.vDeletionFlag) (
	vars.vDbRecord update {
		case STATUS at .STATUS ->  "COMPLETED"
	    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
	}
)
else (
	vars.vDbRecord update {
		case STATUS at .STATUS ->  "FAILED"
	    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
	    case ERROR_MSG at .ERROR_MSG -> ((deleteResponse.Table[0].Column1 default deleteResponse.Table[0].Error) default "")  replace "\"" with ""
	    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
	}
)]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<error-handler>
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="071ac74b-bea2-492d-afad-e0449510645f" type="HTTP:CONNECTIVITY, HTTP:TIMEOUT">
							<set-variable value="#[(vars.vRetryCount as Number) + 1]" doc:name="Update vRetryCount" doc:id="37d79bfc-feff-4f03-a404-d547286cd8a3" variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="3a167f18-37f5-4b8a-b4ce-91941ff9aa9b" type="HTTP:BAD_REQUEST, HTTP:CLIENT_SECURITY, HTTP:FORBIDDEN, HTTP:UNAUTHORIZED">
							<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="dbc818f4-f9fe-444c-be00-2aa9d0534d0f">
								<ee:message />
								<ee:variables>
									<ee:set-variable variableName="vDbRecord"><![CDATA[output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
}]]></ee:set-variable>
									<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="0cd4b722-9b9e-4431-95af-372147a0ae35" name="sf-update-transaction-task-details-table"/>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="94e09265-e5cc-46b7-8081-7d91e3d4f716">
			<ee:message>
				<ee:set-payload><![CDATA[output application/json
var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")
---
if((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vDeletionFlag)) {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "counterpartyID": vars.vCounterpartyId,
    "eid": vars.vEnterpriseId,
    "message": "DELETED"
  }
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDbRecord.'ERROR_TYPE',
      "details": vars.vDbRecord.'ERROR_MSG'
    }
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus"><![CDATA[output application/json
var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")
var deleteFlag = deleteResponse.Table[0].Column1 ~= "Commands completed successfully."
---
if(deleteFlag default false) 200 else 400]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
			<error-handler>
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="d222e286-0cd2-4c4a-9b70-e57190522409" type="ANY">
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="6f9d35e5-b1d2-4eae-9ef2-2780520d4616">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="vDbRecord"><![CDATA[output application/json
---
vars.vDbRecord update {
    case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> p('artemis.retry.maxRetries') as Number
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "SYSTEM"
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes"><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="c9714ed2-dde7-4954-a979-369357a2a15d" name="sf-update-transaction-task-details-table" />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="bba65f7e-83af-4170-ae0f-6b9cd4929af2"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-delete-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="48bb5b56-5ab7-404b-baa8-9d5afda9e0de"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-delete-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="c7b69a5b-0b84-4099-baf5-d80fae655598"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-delete-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
