# pf-get-artemis-account

## Start Logging

- LOG INFO: Log Entry
- LOG INFO: Log Outbound Request
- LOG DEBUG: Log Outbound Request Payload

## Variable: vRequestAttributes

```js
attributes.queryParams.'eid' default ""
```

## Choice: Check If vInternalId Is Empty

```mermaid
flowchart LR
    A{Choice: Check If vCounterpartyId Is Empty}
    A --> B[Exists]
    A --> C[Not Exists]
    B --> B1[[sf-get-artemis-counterparty-id]]  
    C --> C1[[sf-get-artemis-enterprise-id]]
```

## HTTP Request: Get Counterparty Record (Artemis API)

- Path: `"/UDA?database=" ++ p('secure::http.request.uda.endpoint.readDatabase') ++ "&objectType=" ++ p('http.request.uda.endpoint.objectType') ++ "&schema=" ++ p('http.request.uda.endpoint.readSchema') ++ "&objectName=" ++ p('http.request.uda.endpoint.readObjectName') ++ "&filters=where CounterpartyID = '" ++ vars.vCounterpartyId ++ "'"`
- Target Variable: `vRetrieveCounterpartyResponse`

## Transform Message: Set Payload, HTTP Status

```json
{
   "code":200,
   "transactionId": vars.vTransactionId,
   "status":"SUCCESS",
   "response": (
    if(sizeOf(counterpartyRecord.'Table') != 0) {
    "account": (counterpartyRecord.'Table' map() -> {
       "counterpartyName": ($).'CounterpartyName' default null,
       "changeUser": ($).'ChangeUser' default null,
       "greeneFlag": ($).'GreeneFlag' default null,
       "businessStreet": ($).'BusinessStreet' default null,
       "businessStreet2": ($).'BusinessStreet2' default null,
       "businessCity": ($).'BusinessCity' default null,
       "businessState": ($).'BusinessState' default null,
       "businessPostalCode": ($).'BusinessPostalCode' default null,
       "businessCountry": ($).'BusinessCountry' default null,
       "counterpartyID": vars.vCounterpartyId default null,
       "eid": vars.vEnterpriseId default null
    })[0]
  }
  else {
   "message": "RECORD_NOT_FOUND",
   "details": "Record with counterpartyID " ++ vars.vCounterpartyId ++ " doesn't exist in Artemis"
  }
 )
}
```

## End Logging

- LOG DEBUG: Log Outbound Response Payload
- LOG INFO: Log Outbound Response
- LOG INFO: Log Exit
