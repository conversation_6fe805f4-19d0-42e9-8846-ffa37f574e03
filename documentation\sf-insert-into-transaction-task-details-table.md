# sf-insert-into-transaction-task-details-table

## HTTP Request: Post record into TRANSACTION_TASK_DETAILS

- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Database: `Enterprise_ID`
- Table: `dbo.TRANSACTION_TASK_DETAILS`

### Body

```json
{
 "transactionTask": vars.vDbRecord
}
```

### Headers

```js
vars.vRequestAttributes.'headers' default vars.vAttributes.headers
```

### Target Variable

`vInsertDBRecordResponse`
