# pf-delete-artemis-account

## Start Logging

- LOG INFO: Log Entry
- LOG INFO: Log Outbound Request
- LOG DEBUG: Log Outbound Request Payload

## Variable: vRequestAttributes

```json
{
 "headers": {
  "x-source": vars.vAttributes.headers.'x-source' default "",
  "x-transactionId": vars.vTransactionId,
  "x-msg-timestamp": now() as DateTime,
  "correlationId": vars.vCorrelationId,
  "sourceId": "ARTEMIS_SYS_API",
  "destinationId": "TRANSACTION_DB_SYS_API",
  "content-type": "application/json"
 }
}
```

## Variable: vEnterpriseId

```js
attributes.queryParams.'eid' default ""
```

## Choice: Check Header Context

```mermaid
---
title: Check Header Context
---
flowchart LR
    A{Choice: Check header context}
    A{vars.vAttributes.headers.'context'}
    A --> B[REGULAR]
    A --> C[ROLLBACK or RETRY]
    A --> D[CUSTOM:INVALID_HEADER_CONTEXT]
    B --> B1[[sf-insert-into-transaction-task-details-table]]  
    C --> C1[LOG INFO: Skip STATUS update]
    D --> D1["Invalid value for context is passed"]
```

## Choice: Check If vInternalId Is Empty

```mermaid
flowchart LR
    A{Choice: Check If vCounterpartyId Is Empty}
    A --> B[Exists]
    A --> C[Not Exists]
    B --> B1[[sf-get-artemis-counterparty-id]]  
    C --> C1[[sf-get-artemis-enterprise-id]]
```

## Transform Message: Set vDeleteCounterpartyBody

```json
[
    {
        "database": "Artemis",
        "schema": "port",
        "table": "tdt_Counterparty",
        "whereclause": "CounterpartyID = '" ++ vars.vCounterpartyId ++ "'"
    }
]
```

## HTTP Request: Delete Counterparty Record (Artemis API)

- Path: `"/UDA?database=" ++ p('secure::http.request.uda.endpoint.postDatabase') ++ "&objectType=" ++ p('http.request.uda.endpoint.objectType') ++ "&schema=" ++ p('http.request.uda.endpoint.postSchema') ++ "&objectName=" ++ p('http.request.uda.endpoint.deleteObjectName') ++ "&filters=where CounterpartyID = '" ++ vars.vCounterpartyId ++ "'"`
- Body: `vDeleteCounterpartyBody`
- Target Variable: `vDeleteCounterpartyResponse`

## Variable: vDeletion Flag

> creates variable using the `vDeleteCounterpartyResponse` variable created when record posted to Artemis

```js
var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")
var deleteFlag = deleteResponse.Table[0].Column1 ~= "Commands completed successfully."
---
(deleteFlag default false)
```

## Choice: Check if Artemis Deletion is Successful

```mermaid
flowchart LR
    A{Check if Artemis Deletion is Successful}
    A --> C[Not Exists]
    A --> B[Exists]
    C --> C1[LOG WARN: Deletion failure]
```

## Variable: vRequestAttributes Update

```js
vars.vRequestAttributes update {
  case timestamp at .headers.'x-msg-timestamp' -> now()
}
```

## Flow Reference: sf-delete-transaction-task-details-table

[sf-update-transaction-task-details-table](sf-update-transaction-task-details-table.md)

## Transform Message: Set Payload

```json
var deleteResponse = read(vars.vDeleteCounterpartyResponse, "application/json")
---
if((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vDeletionFlag)) {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "counterpartyID": vars.vCounterpartyId,
    "eid": vars.vEnterpriseId,
    "message": "DELETED"
  }
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDbRecord.'ERROR_TYPE',
      "details": vars.vDbRecord.'ERROR_MSG'
    }
}
```

## End Logging

- LOG DEBUG: Log Outbound Response Payload
- LOG INFO: Log Outbound Response
- LOG INFO: Log Exit
