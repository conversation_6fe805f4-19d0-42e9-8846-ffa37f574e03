### API ###
api.autodiscoveryId=19698492

### HTTPS Listener ###
https.listener.keystore.path=certificates/keystore-test.jks
https.listener.truststore.path=certificates/truststore-test.jks

### Artemis ###
artemis.retry.timePeriod=1000
artemis.retry.maxRetries=3
artemis.username.prefix=LUNA

### HTTP Request UDA endpoint ###
https.request.uda.host=3d-web-dev-01.luna.local
https.request.uda.port=443
https.request.uda.basePath=/artemisapi-preprod/api
https.request.uda.connectionTimeout=30000
https.request.uda.responseTimeout=30000
https.request.uda.reconnection.frequency=1000
https.request.uda.reconnection.attempts=3
https.request.uda.endpoint.objectType=Table
https.request.uda.endpoint.readSchema=port
https.request.uda.endpoint.postSchema=dbo
https.request.uda.truststore.path=

### Counterparty ###
https.request.uda.endpoint.insertObjectName=tdp_Web_API_Insert
https.request.uda.endpoint.updateObjectName=tdp_Web_API_Update
https.request.uda.endpoint.deleteObjectName=tdp_Web_API_Delete
https.request.uda.endpoint.readObjectName=tdt_Counterparty
https.request.uda.endpoint.getChangeUserSchema=util
https.request.uda.endpoint.getChangeUserObjectType=StoredProcedure
https.request.uda.endpoint.getChangeUserObjectName=udp_Artemis_GetChangeUser

### Generic Request ###
https.request.uda.endpoint.submitRequestObjectName=tdp_ARQ_SubmitRequest
https.request.uda.endpoint.submitRequestSchema=util

### HTTPS Request Transaction DB ###
https.request.transactionDBSysApi.host=transactiondb-sys-api-test-x3sg1x.internal-1avcn3.usa-w2.cloudhub.io
https.request.transactionDBSysApi.port=443
https.request.transactionDBSysApi.connectionTimeout=30000
https.request.transactionDBSysApi.responseTimeout=30000
https.request.transactionDBSysApi.reconnection.frequency=1000
https.request.transactionDBSysApi.reconnection.attempts=3
https.request.transactionDBSysApi.truststore.path=certificates/truststore-test.jks

### Sleep thread timeperiod ###
artemis.sleepThread.Timeperiod=100