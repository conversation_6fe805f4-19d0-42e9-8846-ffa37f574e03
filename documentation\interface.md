# Interface

## APIKitRouter: 3degreesArtemisSysAPI-main

### Transform Message: Set Attributes

> gets attributes defined in api endpoints

```json
{
 "headers": attributes.headers,
 "queryParams": attributes.queryParams,
 "uriParams": attributes.uriParams
}
```

## Endpoints

> all endpoints call flows defined in IMPLEMENTATION flows

### get:\account

- FlowReference: [pf-get-artemis-account](pf-get-artemis-account.md)

### post:\account

- FlowReference: [pf-create-artemis-account](pf-create-artemis-account.md)

### put:\account

- FlowReference: [pf-update-artemis-account](pf-update-artemis-account.md)

### delete:\account

- FlowReference: [pf-delete-artemis-account](pf-delete-artemis-account.md)
