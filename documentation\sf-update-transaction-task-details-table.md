# sf-update-transaction-task-details-table

## HTTP Request: Update TRANSACTION_TASK_DETAILS record

- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Database: `Enterprise_ID`
- Table: `dbo.TRANSACTION_TASK_DETAILS`

### Body

```json
{
 "transactionTask": vars.vDbRecord
}
```

### Headers

```js
vars.vRequestAttributes.'headers' default vars.vAttributes.headers
```

### Query Parameters

```json
{
 "CORRELATION_ID": vars.vDbRecord.'CORRELATION_ID',
 "DESTINATION": vars.vDbRecord.'DESTINATION'
}
```

### Target Variable

`vInsertDBRecordResponse`
