<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd">
	<flow name="pf-get-artemis-user-details" doc:id="7bc5c35a-96a1-491c-816f-16f4c8c3c825" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry" doc:id="9f936f6e-9efe-4e62-8207-48bab493cb9c" message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-artemis-user-details", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request" doc:id="83b2ce8d-1f6d-4658-acec-a4d88b00c46b" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-get-artemis-user-details",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/users"&#10;}]' />
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Request Payload" doc:id="4388082a-bc5a-449d-ad40-c79860ef4708" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-get-artemis-user-details",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/users",&#10;	"BackendRequest": payload&#10;}]' />
		<set-variable value="#[vars.vAttributes.queryParams.'userId']" doc:name="vUsername" doc:id="4431c823-4544-47b4-a303-d2be2a854a59" variableName="vUsername"/>
		<http:request method="GET" doc:name="Call to retrieve User details" doc:id="08ab31b1-24cd-4337-a919-f3ea763c76fd" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ Mule::p('secure::https.request.uda.endpoint.getChangeUserDatabase') ++ &quot;&amp;schema=&quot; ++ Mule::p('https.request.uda.endpoint.getChangeUserSchema') ++ &quot;&amp;objectType=&quot; ++ Mule::p('https.request.uda.endpoint.getChangeUserObjectType') ++ &quot;&amp;objectName=&quot; ++ Mule::p('https.request.uda.endpoint.getChangeUserObjectName') ++ &quot;&amp;parameters=&quot; ++ &quot;@Username=&quot; ++ Mule::p('artemis.username.prefix') ++ &quot;%5C&quot; ++ (vars.vUsername default &quot;&quot;) ++ &quot;;@ErrorFlag=1&quot;]" target="vRetrieveUserDetailsResponse"/>
		<set-variable value='#[output application/json&#10;var userDetails = read(vars.vRetrieveUserDetailsResponse default "", "application/json")&#10;---&#10;!isEmpty(userDetails.Table[0].EmailAddress)]' doc:name="vUserExistsFlag" doc:id="08f55287-13a9-43a5-8cd6-84b31726c1f4" variableName="vUserExistsFlag"/>
		<choice doc:name="Check if user exists" doc:id="7f2cbaee-dd25-4a72-bd6e-a0e47d5c2046" >
			<when expression="#[vars.vUserExistsFlag default false]">
				<ee:transform doc:name="Set payload, httpStatus" doc:id="4eb7f37f-3ebd-4ece-bdf5-96f18b491bf3" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
var userDetails = read(vars.vRetrieveUserDetailsResponse default "", "application/json")
---
{
	"code": 200,
	"transactionId": vars.vTransactionId,
	"status": "SUCCESS",
	"response": {
		"user": {
			"emailAddress": userDetails.Table[0].'EmailAddress' default null
		}
	 }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</when>
			<otherwise >
				<ee:transform doc:name="Set payload, httpStatus" doc:id="1ebf7c14-38c9-4f6c-bda3-9a932d0d2bd8" >
					<ee:message >
						<ee:set-payload ><![CDATA[output application/json
var userDetails = read(vars.vRetrieveUserDetailsResponse default "", "application/json")
---
{
	"code": 400,
	"transactionId": vars.vTransactionId,
	"status": "FAILURE",
	"response": {
		"message": "User not found",
      	"details": userDetails.Table[0].'Message' default null
	 }
}]]></ee:set-payload>
					</ee:message>
					<ee:variables >
						<ee:set-variable variableName="httpStatus" ><![CDATA[400]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
			</otherwise>
		</choice>
		<logger level="DEBUG" doc:name="LOG DEBUG: Log Outbound Response Payload" doc:id="7493f4c3-22e7-4a6f-af0a-e67e87350437" message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-artemis-user-details",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/users",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response" doc:id="cd3b4085-01f7-4727-8db4-a129b29be060" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-artemis-user-details",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/users"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit" doc:id="3b0a0703-5453-48bc-82c3-4f138bd4f92f" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-artemis-user-details",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
