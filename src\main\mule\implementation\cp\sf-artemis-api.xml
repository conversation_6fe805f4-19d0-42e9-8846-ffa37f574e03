<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd 
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd">
	<sub-flow name="artemis_document_get" doc:id="e5e44bc4-286e-481a-96fb-55ca0169c93b" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="5a19cf5b-9687-4930-b3b4-353224b02c4c" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Retrieving Document" doc:id="703b85a0-7122-4b1a-a75a-764d9f23e219" >
			<http:request method="GET" doc:name="Artemis API Document" doc:id="07196980-99f4-4121-a774-753d49d9bfc3" path="/Document/Get" sendBodyMode="NEVER" config-ref="HTTPS_Request_UDA_Endpoint">
				<http:headers ><![CDATA[#[output application/java
---
{
	Accept : attributes.headers.accept
}]]]></http:headers>
				<http:query-params ><![CDATA[#[output application/java
---
{
	DocumentID : attributes.queryParams.documentID
}]]]></http:query-params>
			</http:request>
			<logger level="INFO" doc:name="LOG INFO: Artemis API Document" doc:id="c7af1a02-a681-4d8a-adba-da2bd9a236b4" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: TABLE GET",&#10;	payload: payload&#10;}]'/>
			<choice doc:name="Is Successful?" doc:id="b80be91b-916d-485c-b081-44e6faafdc12">
			<when expression='#[payload ~= "There is no row at position 0."]'>
				<ee:transform doc:name="Artemis Error" doc:id="08f752a4-3b63-40fa-bae4-2b84cedb9524">
					<ee:message>
					</ee:message>
					<ee:variables>
						<ee:set-variable variableName="artErrorResponse"><![CDATA[%dw 2.0
output application/java
---
{
	errors: "DocumentID not found."
}]]></ee:set-variable>
					</ee:variables>
				</ee:transform>
				<raise-error doc:name="ERROR: Document Not Found" doc:id="10625173-ea9f-4a6c-bd7d-7ed2a35aa651" type="APP:ARTEMIS_API_DOCUMENT_NOT_FOUND" description="ARTEMIS API: DOCUMENT NOT FOUND" />
			</when>
				<when expression='#[(!(payload contains("Unauthorized")) and payload != "There is no row at position 0.") and !(payload contains("PDF"))]'>
					<ee:transform doc:name="Artemis Error" doc:id="5e20fc39-f05b-4ac9-8ca3-9f4e83d2ff80" >
						<ee:message />
						<ee:variables >
							<ee:set-variable variableName="artErrorResponse" ><![CDATA[%dw 2.0
output application/java
---
{
	errors: payload
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Default" doc:id="4b5e911a-ece3-4021-b43f-fa5d6e12aafc" type="APP:ARTEMIS_API_DOCUMENT_DEFAULT" description="ARTEMIS API: DEFAULT" />
				</when>
				<otherwise>
				<ee:transform doc:name="payload" doc:id="7ca18e2e-6418-4d1e-966f-a18da64b7346">
					<ee:message>
						<ee:set-payload><![CDATA[payload]]></ee:set-payload>
					</ee:message>
				</ee:transform>
			
</otherwise>
		</choice>
			<error-handler ref="global-error-handler" />
			
		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="b44eadff-4378-4fee-9463-188c97b8a5c1" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: vars.flowName,&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
	<sub-flow name="artemis_uda_stored-procedure" doc:id="dcc06eb3-07cc-4b41-a587-c9d50a327f81" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="e97ca54f-422f-4412-b240-f316e43305ef" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	FlowName: "artemis_uda_stored-procedure",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Executing Stored Procedure" doc:id="855a9b24-7c90-44ba-a40e-6e1ea6ce2fe2" >
			<http:request method="GET" doc:name="Artemis API Stored Procedure" doc:id="a0efe5a6-24e7-49a2-bfbe-71f0209dd88d" sendBodyMode="NEVER" config-ref="HTTPS_Request_UDA_Endpoint" path="/UDA">
				<http:query-params ><![CDATA[#[output application/java
---
{
	database: attributes.queryParams.database,
	schema: attributes.queryParams.schema,
	objectType: attributes.queryParams.objectType,
	objectName: attributes.queryParams.objectName,
	parameters: "$(attributes.queryParams.parameters default '' as String)"
}]]]></http:query-params>
			</http:request>
			<ee:transform doc:name="payload" doc:id="33d6c122-93a8-4837-9f4a-6b2fbc5e5c0d" >
				<ee:message >
					<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
read(payload as String,"application/JSON") as Object]]></ee:set-payload>
				</ee:message>
			</ee:transform>
			<logger level="INFO" doc:name="LOG INFO: Artemis API Stored Procedure" doc:id="27346f9a-72cf-493c-8b2a-a5ee77e074f7" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: STORED PROCEDURE GET",&#10;	payload: payload&#10;}]'/>
			<choice doc:name="Is Successful?" doc:id="d5d99332-23b4-469d-9f73-0fc1cd5aaea9" >
				<when expression='#[!isEmpty(payload.Table1[0].Error)]'>
					<ee:transform doc:name="Artemis Error" doc:id="7b8f9045-3385-4a72-aff0-c05a4c896121" >
						<ee:variables >
							<ee:set-variable variableName="artErrorResponse" ><![CDATA[%dw 2.0
output application/java
---
{
	errors: payload.Table1[0].Error
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Artemis Error" doc:id="9e405f19-e333-4746-91b8-209d83241f09" type="APP:ARTEMIS_API_STORED_PROCEDURE" description="ARTEMIS API: STORED PROCEDURE ERROR" />
				</when>
				<otherwise >
					<ee:transform doc:name="payload" doc:id="16e072b2-4f5a-4e94-b2ac-bc7b6dd5db96" >
						<ee:message >
							<ee:set-payload ><![CDATA[output application/java
---
payload]]></ee:set-payload>
						</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
					</ee:transform>
				</otherwise>
			</choice>
			<error-handler ref="global-error-handler" />
			
		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="8d104a20-60f3-4e46-bcb8-7eb2bbf88611" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	FlowName: "artemis_uda_stored-procedure",&#10;	CorrelationID: vars.request.correlationID&#10;}]' />
	</sub-flow>
	<sub-flow name="artemis_uda_table" doc:id="114a8196-cc9f-45aa-8c58-7dec8d340658" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="b207bdde-f0d8-4b4d-9553-37a6c9f6f5ad" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "system-flows\artemis\sf-artemis-api\artemis_uda_table",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Retrieving Table Data" doc:id="1e683a02-d50f-4f5c-8ffc-02e79d5b7152" >
			<http:request method="GET" doc:name="Artemis API Table" doc:id="ee22b845-9057-4f8c-a9df-f777b206c494" path="/UDA" sendBodyMode="NEVER" config-ref="HTTPS_Request_UDA_Endpoint">
			<http:query-params><![CDATA[#[output application/java
---
{
	database: attributes.queryParams.database,
	schema: attributes.queryParams.schema,
	objectType: attributes.queryParams.objectType,
	objectName: attributes.queryParams.objectName,
	filters: "$(attributes.queryParams.filters default '' as String)"
}]]]></http:query-params>
		</http:request>
			<ee:transform doc:name="payload" doc:id="5431d0ba-2aa8-4299-9b97-63290e372bdb">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
read(payload as String,"application/JSON") as Object]]></ee:set-payload>
			</ee:message>
		</ee:transform>
			<logger level="INFO" doc:name="LOG INFO: Artemis API Table" doc:id="390f7d92-b0c1-414d-a916-06eb577f6786" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: TABLE GET",&#10;	payload: payload&#10;}]'/>
			<choice doc:name="Is Successful?" doc:id="19f8c811-9e83-42b7-bcd5-1b1890b13237" >
				<when expression='#[!isEmpty(payload.Table1[0].Error)]'>
					<ee:transform doc:name="Artemis Error" doc:id="8454f159-754b-4f2d-b569-568ac9b74801">
						
						<ee:variables>
							<ee:set-variable variableName="artErrorResponse"><![CDATA[%dw 2.0
output application/java
---
{
	errors: payload.Table1[0].Error
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Artemis API Table" doc:id="7781772d-604c-41b4-a60a-20b67e3c8729" type="APP:ARTEMIS_API_TABLE" description="ARTEMIS API: GET TABLE ERROR"/>
				</when>
				<otherwise>
					<ee:transform doc:name="payload" doc:id="3f7b6158-752d-403c-a16f-6592be2fd2d8" >
						<ee:message >
							<ee:set-payload ><![CDATA[%dw 2.0
output application/java
---
payload]]></ee:set-payload>
						</ee:message>
					</ee:transform>
				</otherwise>
			</choice>
			<error-handler ref="global-error-handler" />

		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="125e8af4-f0b5-4389-9ced-887143ea7351" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "system-flows\artemis\sf-artemis-api\artemis_uda_table",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
	<sub-flow name="artemis_uda_insert" doc:id="35ba13bb-c2bb-40f8-b6c1-b0c215d1d6bd" >
		<logger level="INFO" doc:name="LOG INFO: Flow Start" doc:id="ee1a4a49-21a2-4c97-916a-6fca2659bc23" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW START",&#10;	Flow: "systems-flowss\artemis\sf-artemis-api\artemis_uda_insert",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
		<try doc:name="Try Inserting Payload" doc:id="775e8de6-064d-4d88-a478-0c74fe102d90" >
			<ee:transform doc:name="Artemis Request" doc:id="1ff54c35-e132-4c89-8979-49746b8b5fad">
					
				<ee:variables>
					<ee:set-variable variableName="artInsertRequest"><![CDATA[%dw 2.0
output application/json
---
{
	database: payload[0].database,
	schema: payload[0].schema,
	table: payload[0].table,
	value: payload[0].value
}]]></ee:set-variable>
				</ee:variables>
				</ee:transform>
			<foreach doc:name="For Each Record" doc:id="d0ebf342-aa03-4140-bc9d-e30b681d59b9" collection="#[output application/json&#10;---&#10;vars.artInsertRequest.value]">
				<http:request method="POST" doc:name="Artemis API Insert" doc:id="25f5d16f-0800-443a-ab63-b9d4de464ee3" config-ref="HTTPS_Request_UDA_Endpoint" path="/UDA" sendBodyMode="ALWAYS" target="artInsertResponse">
				<http:body><![CDATA[#[output application/json
---
[{
	database: vars.artInsertRequest.database,
	schema: vars.artInsertRequest.schema,
	table: vars.artInsertRequest.table,
	value: [payload]
}]]]]></http:body>
				<http:headers><![CDATA[#[output application/java
---
{
	"Accept": "text/plain",
	"Content-Type": "application/json"
}]]]></http:headers>
				<http:query-params><![CDATA[#[output application/json
---
{
	database: Mule::p('artemis.api.https.request.query.database'),
	schema: Mule::p('artemis.api.https.request.query.schema'),
	objectName: Mule::p('artemis.api.https.request.query.insert.objectName')
}]]]></http:query-params>
			</http:request>
				<logger level="INFO" doc:name="LOG INFO: Artemis API Insert" doc:id="46ea38c8-1974-4e51-9467-a36f2c487d06" message='#[output application/json&#10;---&#10;{&#10;	message: "ARTEMIS API: TABLE INSERT",&#10;	payload: vars.artInsertResponse.Table&#10;}]'/>
				<choice doc:name="Is Successful?" doc:id="a33fa8d7-81f1-442d-8461-a6cc2e157b67">
				<when expression='#[vars.artInsertResponse contains("Error")]'>
					<ee:transform doc:name="Artemis Error" doc:id="53960ff4-57b7-4c84-969c-2f1ef3a192bc">
						<ee:message />
						<ee:variables>
							<ee:set-variable variableName="artErrorResponse"><![CDATA[%dw 2.0
output application/json
---
{
	errors: read(vars.artInsertResponse,"application/JSON").Table[0].Column1
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<raise-error doc:name="ERROR: Insert Failure" doc:id="5dfe7a01-1c31-4fc9-89ac-6a65c994f3a6" type="APP:ARTEMIS_API_INSERT_FAILURE" description="ARTEMIS API: INSERT ERROR" />
				</when>
					<otherwise>
						<ee:transform doc:name="payload" doc:id="5fd60933-49d8-484c-a96b-9235cf52fb05">
						<ee:message>
							<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	message: "SUCCESS: RECORDS POSTED TO ARTEMIS",
	details: read(vars.artInsertResponse,"application/JSON").Table[0].Column1
}]]></ee:set-payload>
						</ee:message>
					</ee:transform>
				</otherwise>
			</choice>
			</foreach>
			<ee:transform doc:name="payload" doc:id="00e68c5e-79ee-4d67-9beb-959f7deae8ae">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/java
---
{
	message: "SUCCESS: RECORDS POSTED TO ARTEMIS",
	details: ""
}]]></ee:set-payload>
			</ee:message>
		</ee:transform>
			<error-handler ref="global-error-handler" />
		</try>
		<logger level="INFO" doc:name="LOG INFO: Flow End" doc:id="c04815c4-4671-49d5-9d4c-c76294363782" message='#[output application/json&#10;---&#10;{&#10;	Message: "FLOW END",&#10;	Flow: "systems-flowss\artemis\sf-artemis-api\artemis_uda_insert",&#10;	CorrelationId: vars.request.correlationID&#10;}]' />
	</sub-flow>
</mule>
