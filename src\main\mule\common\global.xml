<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit"
	xmlns:api-gateway="http://www.mulesoft.org/schema/mule/api-gateway"
	xmlns:tls="http://www.mulesoft.org/schema/mule/tls"
	xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:secure-properties="http://www.mulesoft.org/schema/mule/secure-properties"
	xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd 
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd
http://www.mulesoft.org/schema/mule/secure-properties http://www.mulesoft.org/schema/mule/secure-properties/current/mule-secure-properties.xsd
http://www.mulesoft.org/schema/mule/tls http://www.mulesoft.org/schema/mule/tls/current/mule-tls.xsd
http://www.mulesoft.org/schema/mule/api-gateway http://www.mulesoft.org/schema/mule/api-gateway/current/mule-api-gateway.xsd">
	
    	
    
	<tls:context name="TLS_Context_Inbound"
		doc:name="TLS Context" doc:id="90e84bb8-527e-4be0-9301-a0b9b78adc5c">
		<tls:key-store type="jks"
			path="${https.listener.keystore.path}"
			keyPassword="${secure::https.listener.keystore.keyPassword}"
			password="${secure::https.listener.keystore.password}" />
	</tls:context>
	<tls:context name="TLS_Context_Transaction_DB_Outbound"
		doc:name="TLS Context" doc:id="a646a172-f12f-454f-967e-0ad7b6820451">
		<tls:trust-store
			path="${https.request.transactionDBSysApi.truststore.path}"
			password="${secure::https.request.transactionDBSysApi.truststore.password}"
			type="jks" />
	</tls:context>
	
	<!-- TBD when truststore details are given
	<tls:context name="TLS_Context_UDA_Outbound" doc:name="TLS Context" doc:id="b878a0e3-0acb-4fe3-b5c5-c8f530d66155" >
		<tls:trust-store path="${https.request.uda.truststore.path}" password="${secure::https.request.uda.truststore.password}" type="jks" />
	</tls:context> 
	-->
	
	<configuration-properties doc:name="Configuration properties" doc:id="ee8b5714-bcfb-4567-a880-9ae0e5798eb6" file="config\config-common.properties" />
	<configuration-properties doc:name="Configuration properties" doc:id="86c521dc-6df6-4f33-8253-61fd6cef02d1" file="config\config-${mule.env}.properties" />
	<secure-properties:config name="Secure_Properties_Config" doc:name="Secure Properties Config" doc:id="06e02d7e-d356-42b6-90ba-b5fc456a5e04" file="config\config-${mule.env}-secure.properties" key="${mule.key}" >
		<secure-properties:encrypt algorithm="Blowfish" />
	</secure-properties:config>
	<configuration doc:name="Configuration" doc:id="8853fea1-ac72-48db-ab31-7ea3723e16ba" defaultErrorHandler-ref="global-error-handler" />
	<!-- <db:config name="Database_Config_Transactions" doc:name="Database Config" doc:id="14f64d77-9e76-482c-acfe-44a624e4b17f" >
		<db:my-sql-connection host="${secure::db.host}" port="${db.port}" user="${secure::db.username}" database="${secure::db.database}" >
			<reconnection >
				<reconnect frequency="${db.reconnection.frequency}" count="${db.reconnection.attempts}" />
			</reconnection>
		</db:my-sql-connection>
	</db:config> -->
	
	<http:request-config
		name="HTTPS_Request_Transaction_DB_SYS_API"
		doc:name="HTTP Request configuration"
		doc:id="fcfb5c85-53a2-469f-8f2f-53fe23176d05">
		<http:request-connection
			host="${https.request.transactionDBSysApi.host}" protocol="HTTPS"
			port="${https.request.transactionDBSysApi.port}"
			connectionIdleTimeout="${https.request.transactionDBSysApi.connectionTimeout}" tlsContext="TLS_Context_Transaction_DB_Outbound">
			<reconnection>
				<reconnect
					frequency="${https.request.transactionDBSysApi.reconnection.frequency}"
					count="${https.request.transactionDBSysApi.reconnection.attempts}" />
			</reconnection>
		</http:request-connection>
		<http:default-headers>
			<http:default-header key="client_id"
				value="#[p('secure::https.request.transactionDBSysApi.headers.clientId')]" />
			<http:default-header key="client_secret"
				value="#[p('secure::https.request.transactionDBSysApi.headers.clientSecret')]" />
		</http:default-headers>
	</http:request-config>
	
	<http:request-config name="HTTPS_Request_UDA_Endpoint" doc:name="HTTP Request configuration" doc:id="02196e9e-d382-4aba-908a-1a6e2ffe174c" basePath="#[p('https.request.uda.basePath')]" responseTimeout="#[p('https.request.uda.responseTimeout')]">
		<http:request-connection host="${https.request.uda.host}" port="${https.request.uda.port}" connectionIdleTimeout="${https.request.uda.connectionTimeout}" protocol="HTTPS">
			<reconnection >
				<reconnect frequency="${https.request.uda.reconnection.frequency}" count="${https.request.uda.reconnection.attempts}" />
			</reconnection>
			<tls:context >
				<tls:trust-store insecure="true" />
			</tls:context>
		</http:request-connection>
		<http:default-headers >
			<http:default-header key="Accept" value="text/plain" />
			<http:default-header key="ClientID" value="${secure::https.request.uda.headers.clientId}" />
			<http:default-header key="ClientSecret" value="${secure::https.request.uda.headers.clientSecret}" />
		</http:default-headers>
	</http:request-config>
	<api-gateway:autodiscovery apiId="${api.autodiscoveryId}" ignoreBasePath="true" doc:name="API Autodiscovery" doc:id="41a7eeb7-8cb3-4c84-a1ad-cf0af12f03be" flowRef="3degreesArtemisSysAPI-main" />
</mule>
