<?xml version="1.0" encoding="UTF-8"?>
<mule xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:apikit="http://www.mulesoft.org/schema/mule/mule-apikit" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core" xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/mule-apikit http://www.mulesoft.org/schema/mule/mule-apikit/current/mule-apikit.xsd http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd ">
    <apikit:config name="3degreesArtemisSysAPI-config" api="resource::a970b687-ceb1-48a0-9bc7-6fed0e331363:3degrees-artemis-sys-api:1.0.41:raml:zip:3degreesArtemisSysAPI.raml" outboundHeadersMapName="outboundHeaders" httpStatusVarName="httpStatus" />
    <http:listener-config name="HTTPS_Listener_config" doc:name="HTTP Listener config" doc:id="f2b79a82-625f-46db-8a38-0b5d5fcae18e">
        <http:listener-connection host="${https.listener.host}" port="${https.listener.port}" readTimeout="${https.listener.readTimeout}" connectionIdleTimeout="${https.listener.idleTimeout}" protocol="HTTPS" tlsContext="TLS_Context_Inbound" />
    </http:listener-config>
    <flow name="3degreesArtemisSysAPI-main">
        <http:listener path="/api/*" doc:name="/api/*" config-ref="HTTPS_Listener_config">
            <http:response statusCode="#[vars.httpStatus default 200]">
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:response>
            <http:error-response statusCode="#[vars.httpStatus default 500]">
                <http:body><![CDATA[#[payload]]]></http:body>
                <http:headers><![CDATA[#[vars.outboundHeaders default {}]]]></http:headers>
            </http:error-response>
        </http:listener>
        <ee:transform doc:name="Set vCorrelationId, vTransactionId, vAttributes, vBusinessKey" doc:id="d3a3c3b2-c0fe-480b-bb48-58860af59992">
            <ee:message />
            <ee:variables>
                <ee:set-variable variableName="vCorrelationId"><![CDATA[attributes.headers.'correlationId' default ""]]></ee:set-variable>
                <ee:set-variable variableName="vTransactionId"><![CDATA[attributes.headers.'x-transactionId' default ""]]></ee:set-variable>
                <ee:set-variable variableName="vAttributes"><![CDATA[%dw 2.0
output application/json
---
{
	"headers": attributes.headers,
	"queryParams": attributes.queryParams,
	"uriParams": attributes.uriParams
}]]></ee:set-variable>
                <ee:set-variable variableName="vBusinessKey"><![CDATA[attributes.headers.'x-businessKey' default ""]]></ee:set-variable>
            </ee:variables>
        </ee:transform>
        <apikit:router config-ref="3degreesArtemisSysAPI-config" />
    </flow>
    <flow name="put:\account:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-update-artemis-account" doc:id="afc58837-ac5b-4942-9b8c-67892201f615" name="pf-update-artemis-account" />
    </flow>
    <flow name="get:\account:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-get-artemis-account" doc:id="30bc62ed-6da2-466f-bb44-3b44162b8fba" name="pf-get-artemis-account" />
    </flow>
    <flow name="post:\account:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-create-artemis-account" doc:id="7e9e3771-5fe3-4f12-8547-874ed471ab22" name="pf-create-artemis-account" />
    </flow>
    <flow name="delete:\account:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-delete-artemis-account" doc:id="2c9f071c-f5bc-4944-8c45-966199e2fd99" name="pf-delete-artemis-account" />
    </flow>
    <flow name="get:\users:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-get-artemis-user-details" doc:id="f77483f2-8c2c-4781-9824-52ba8437c68a" name="pf-get-artemis-user-details" />
    </flow>
    <flow name="put:\syncStatuses:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-update-artemis-sync-statuses" doc:id="55749c1a-ff1c-46d2-9366-7fa0090a8875" name="pf-update-artemis-sync-statuses" />
    </flow>
    <flow name="post:\confirms:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-create-artemis-confirm" doc:id="796f98f9-c64c-42b1-8722-99dcbd898717" name="pf-create-artemis-confirm" />
    </flow>
    <flow name="put:\recordIDs:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-update-artemis-record-ids" doc:id="beecbec8-dcf1-4b14-80a7-cb7585bd5aa9" name="pf-update-artemis-record-ids" />
    </flow>
    <flow name="put:\invoices:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-update-artemis-invoice" doc:id="ac4f2ff7-3962-4a63-948e-615b3da18469" name="pf-update-artemis-invoice" />
    </flow>
    <flow name="put:\product:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-update-artemis-product" doc:id="8930e0cc-761a-41c0-b32f-ca63d4e957e3" name="pf-update-artemis-product" />
    </flow>
    <flow name="get:\artemis\document:3degreesArtemisSysAPI-config">
       <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="97f9f151-a44e-4096-b420-f768eeae7f88" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Document" doc:id="4b22ec59-5df4-4e16-b5c7-a59c0adebbd2" name="artemis_document_get" />
		<ee:transform doc:name="Outbound Response" doc:id="afa233cd-80b4-46db-819a-b73259910fd8">
			<ee:message>
				<ee:set-payload><![CDATA[payload]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
	
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="2896ab60-278e-45a5-90b1-386abf9d2e61" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE"&#10;	&#10;}]' />
		

    </flow>
    <flow name="get:\artemis\stored-procedure:3degreesArtemisSysAPI-config">
        <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="979c3d7a-0688-4298-a321-a291569696b0" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Stored Procedure" doc:id="8fe72b7b-fb7f-4e1d-aa4e-4fc26d0d0b27" name="artemis_uda_stored-procedure" />
		<ee:transform doc:name="Outbound Response" doc:id="c1ecef66-824e-4cae-b813-836fff7bba6d">
			<ee:message>
				<ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.success.ok.code') as Number,
	reasonPhrase: Mule::p('apikit.success.ok.description'),
	response: payload
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="35c9a64d-38a3-45b3-b8e6-cf2cf55d5c24" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE"&#10;	&#10;}]' />
    </flow>
    <flow name="get:\artemis\table:3degreesArtemisSysAPI-config">
    <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="ad3ca308-38da-4f07-9f74-e9405c90dcd7" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Table" doc:id="e4b3365f-0fbe-445b-a2f8-168a42893ec6" name="artemis_uda_table"/>
		<ee:transform doc:name="Outbound Response" doc:id="91cc2519-11dd-4562-b87c-33c4cbc3f676" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	statusCode: Mule::p('apikit.success.ok.code') as Number,
	reasonPhrase: Mule::p('apikit.success.ok.description'),
	response: payload
}]]></ee:set-payload>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.ok.description')]]></ee:set-variable>
			</ee:variables>
	
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="34f107cf-34a5-4ad4-b477-52243d60a04f" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE"&#10;	&#10;}]' />
    </flow>
    <flow name="post:\artemis\insert:application\json:3degreesArtemisSysAPI-config">
    <logger level="INFO" doc:name="LOG INFO: Inbound Request" doc:id="0faa2c24-3e60-4ff6-b305-f0593b8ad282" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "INBOUND REQUEST",&#10;	"Request": vars.request&#10;}]' />
		<flow-ref doc:name="Artemis API Insert" doc:id="e9fc349d-ad1f-40e7-b792-f342a5d94c65" name="artemis_uda_insert" />
		<ee:transform doc:name="Outbound Response" doc:id="78d653df-0efb-447b-92d2-11aff3996b07" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
output application/json
---
{
	message: payload.message,
	details: payload.details
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.created.code') as Number]]></ee:set-variable>
				<ee:set-variable variableName="httpReasonPhrase" ><![CDATA[%dw 2.0
output application/java
---
Mule::p('apikit.success.created.description')]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="INFO" doc:name="LOG INFO:  Outbound Response" doc:id="bfdcfaad-1852-4612-a5a9-a6d65313adaa" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "OUTBOUND RESPONSE",&#10;	"Response": payload&#10;}]' />
	</flow>
    <flow name="post:\product:application\json:3degreesArtemisSysAPI-config">
        <ee:transform doc:name="Transform Message">
            <ee:message>
                <ee:set-payload><![CDATA[%dw 2.0
output application/json
---
{
  code: 201,
  transactionId: "123123",
  status: "SUCCESS",
  response: {
    eid: "EID_COMM_0000_1234"
  }
} as Object {encoding: "UTF-8", mediaType: "application/json"}]]></ee:set-payload>
            </ee:message>
        </ee:transform>
    </flow>
    <flow name="post:\counterParty:application\json:3degreesArtemisSysAPI-config">
        <flow-ref doc:name="Flow Reference to pf-create-artemis-counterParty" doc:id="f0a8455e-2740-4e03-9dd6-273b4138fb1c" name="pf-create-artemis-counterParty" />
    </flow>
</mule>
