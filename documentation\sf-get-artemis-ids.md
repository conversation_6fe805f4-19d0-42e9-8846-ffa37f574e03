# sf-get-artemis-ids

## Flow: sf-get-artemis-counterparty-id

### HTTP Request: Retrieve from REF_ID (Counterparty ID)

- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Database: `Enterprise_ID`
- Table: `dbo.REF_ID`

#### Headers: ARTEMIS_ID

```js
vars.vRequestAttributes.'headers' default vars.vAttributes.headers
```

#### Target Variable: ARTEMIS_ID

`vRefIdResponse`

## Payload: ARTEMIS_ID

`vars.vRefIdResponse.response[0].'ARTEMIS_ID' default ""`

---

## Flow: sf-get-artemis-enterprise-id

### HTTP Request: Retrieve from REF_ID (Enterprise ID)

- Configuration: `HTTPS_Request_Transaction_DB_SYS_API`
- Database: `Enterprise_ID`
- Table: `dbo.REF_ID`

#### Headers: ENTERPRISE_ID

```js
vars.vRequestAttributes.'headers' default vars.vAttributes.headers
```

#### Query Parameters: ENTERPRISE_ID

```json
{
 "OBJECT_TYPE": "ACCOUNT",
 "ARTEMIS_ID" : vars.vCounterpartyId
}
```

#### Target Variable: ENTERPRISE_ID

`vRefIdResponse`

## Payload: ENTERPRISE_ID

`vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default ""`
