<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:xero-accounting="http://www.mulesoft.org/schema/mule/xero-accounting" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/xero-accounting http://www.mulesoft.org/schema/mule/xero-accounting/current/mule-xero-accounting.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-get-artemis-account" doc:id="0445e8be-8386-4daa-80c1-0dc424a74c53" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="88b9d807-9316-4df4-bd36-330b976a6ffc"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-get-artemis-account", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="0f7de2de-2e60-4201-b5b9-f75b460c6799"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-get-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="c84f40d8-c17f-46ba-89fe-1a8ef040ffdd"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-get-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vCounterpartyId, vEnterpriseId, vRequestAttributes" doc:id="ea206860-150e-47eb-8419-d0d73d4db718" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vCounterpartyId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'counterpartyID' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "ARTEMIS_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
				<ee:set-variable variableName="vEnterpriseId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
			

</ee:variables>
		</ee:transform>
		<choice doc:name="Check if vInternalId is empty" doc:id="d93ffd5c-fa53-4988-8ccf-0dba862e14cc" >
			<when expression="#[isEmpty(vars.vCounterpartyId)]">
				<flow-ref doc:name="Flow Reference to sf-get-artemis-id" doc:id="193dad8c-1dec-4471-8acd-f1693a78b813" name="sf-get-artemis-id" target="vCounterpartyId"/>
			</when>
			<otherwise >
				<flow-ref doc:name="Flow Reference to sf-get-artemis-enterprise-id" doc:id="0c2d8b81-68da-4a86-8427-5213f7236812" name="sf-get-artemis-enterprise-id" target="vEnterpriseId" />
			</otherwise>
		
</choice>
		<!-- <db:select doc:name="Retrieve from Artemis" doc:id="20d21b6d-7b60-49c8-ad3b-f6007af65f37" config-ref="Database_Config_Transactions" target="vRetrieveCounterpartyResponse">
			<db:sql ><![CDATA[SELECT * FROM tdt_Counterparty WHERE CounterpartyID = :counterpartyId]]></db:sql>
			<db:input-parameters ><![CDATA[#[{
	"counterpartyId": vars.vCounterpartyId
}]]]></db:input-parameters>
		</db:select> -->
		<http:request method="GET" doc:name="Fetch Counterparty record from Artemis" doc:id="045bddce-abd0-4ad3-90d0-7ab2716322de" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.readDatabase') ++ &quot;&amp;objectType=&quot; ++ p('https.request.uda.endpoint.objectType') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.readSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.readObjectName') ++ &quot;&amp;filters=where CounterpartyID = '&quot; ++ vars.vCounterpartyId ++ &quot;'&quot;]" target="vRetrieveCounterpartyResponse">
		</http:request>
		
		<set-variable value="#[output application/json&#10;var counterpartyRecord = read(vars.vRetrieveCounterpartyResponse default &quot;&quot;, &quot;application/json&quot;)&#10;---&#10;sizeOf(counterpartyRecord.'Table') != 0]" doc:name="vRecordExistsFlag" doc:id="31ac939f-4c77-4a91-be85-e5b5fd08d60d" variableName="vRecordExistsFlag"/>
		<choice doc:name="Check if record exists" doc:id="601abfc0-d9f0-47fc-b24a-9732e3feb2ac" >
			<when expression="#[vars.vRecordExistsFlag default false]">
				<set-variable value="#[%dw 2.0&#10;import substringAfter from dw::core::Strings&#10;output application/json&#10;var counterpartyRecord = read(vars.vRetrieveCounterpartyResponse default &quot;&quot;, &quot;application/json&quot;)&#10;---&#10;substringAfter((counterpartyRecord.'Table'[0].'ChangeUser' default &quot;&quot;), &quot;LUNA\\&quot;)]" doc:name="vUsername" doc:id="1b305ec6-f3ae-4fe0-b15f-a023e658ed80" variableName="vUsername"/>
				<http:request method="GET" doc:name="Call to retrieve User details" doc:id="20e3691d-99f9-4440-9136-73842bab069c" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ Mule::p('secure::https.request.uda.endpoint.getChangeUserDatabase') ++ &quot;&amp;schema=&quot; ++ Mule::p('https.request.uda.endpoint.getChangeUserSchema') ++ &quot;&amp;objectType=&quot; ++ Mule::p('https.request.uda.endpoint.getChangeUserObjectType') ++ &quot;&amp;objectName=&quot; ++ Mule::p('https.request.uda.endpoint.getChangeUserObjectName') ++ &quot;&amp;parameters=&quot; ++ &quot;@Username=&quot; ++ Mule::p('artemis.username.prefix') ++ &quot;%5C&quot; ++ (vars.vUsername default &quot;&quot;) ++ &quot;;@ErrorFlag=1&quot;]" target="vRetrieveUserDetailsResponse" />
			</when>
		</choice>
		
		<ee:transform doc:name="Set payload, httpStatus" doc:id="592e21e2-e7fe-4b97-9a42-7c97be92f099" >
			<ee:message >
				<ee:set-payload ><![CDATA[%dw 2.0
import substringBeforeLast from dw::core::Strings
output application/json
var counterpartyRecord = read(vars.vRetrieveCounterpartyResponse default "", "application/json")
fun getUsernameFromEmail(email) = (
	substringBeforeLast((email default ""), "@")
)
---
{
   "code":200,
   "transactionId": vars.vTransactionId,
   "status":"SUCCESS",
   "response": (
	   if(sizeOf(counterpartyRecord.'Table') != 0) do {
	   	  var userDetails = read(vars.vRetrieveUserDetailsResponse default "", "application/json")
	   	  ---
		  "account": (counterpartyRecord.'Table' map() -> {
			    "counterpartyName": ($).'CounterpartyName' default null,
			    "changeUser": (
			    	if(($).'ChangeUser' startsWith "LUNA\\") (
			    		if(!isEmpty(userDetails.Table[0].'EmailAddress')) "LUNA\\" ++ getUsernameFromEmail(userDetails.Table[0].'EmailAddress')
			    		else null
			    	)
			    	else ($).'ChangeUser' default null
			   	),
			    "greeneFlag": ($).'GreeneFlag' default null,
			    "businessStreet": ($).'BusinessStreet' default null,
			    "businessStreet2": ($).'BusinessStreet2' default null,
			    "businessCity": ($).'BusinessCity' default null,
			    "businessState": ($).'BusinessState' default null,
			    "businessPostalCode": ($).'BusinessPostalCode' default null,
			    "businessCountry": ($).'BusinessCountry' default null,
			    "counterpartyID": vars.vCounterpartyId default null,
			    "syncStatus": ($).'SyncStatus' default null,
			    "syncMessage": ($).'SyncMessage' default null,
			    "eid": vars.vEnterpriseId default null
		  })[0]
		}
		else {
			"message": "RECORD_NOT_FOUND",
			"details": "Record with counterpartyID " ++ vars.vCounterpartyId ++ " doesn't exist in Artemis"
		}
	)
}]]></ee:set-payload>
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="httpStatus" ><![CDATA[200]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="ce3ee007-4d0a-4549-82fa-a35752cad027"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-get-artemis-account",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/account",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="71f454f0-773b-4990-86a1-b14343f29106"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-get-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/account"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="b525d517-6d2a-4e59-9248-bb1afffb7280"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-get-artemis-account",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
