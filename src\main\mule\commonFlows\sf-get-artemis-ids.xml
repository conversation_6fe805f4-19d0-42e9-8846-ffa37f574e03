<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http"
	xmlns:db="http://www.mulesoft.org/schema/mule/db" xmlns="http://www.mulesoft.org/schema/mule/core"
	xmlns:doc="http://www.mulesoft.org/schema/mule/documentation"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd">
	<sub-flow name="sf-get-artemis-id" doc:id="5b179a72-8151-4bc6-96c3-166ae9617b1f" >
		<http:request method="GET" doc:name="Retrieve from REF_ID" doc:id="4dcd27e9-93b6-4a7d-b737-fcb49d031d45" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/REF_ID" target="vRefIdResponse">
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"ENTERPRISE_ID" : vars.vEnterpriseId
}]]]></http:query-params>
		</http:request>
		<set-payload value="#[output application/json&#10;---&#10;vars.vRefIdResponse.response[0].'ARTEMIS_ID' default &quot;&quot;]" doc:name="Set Payload" doc:id="90d545cf-5b4a-43c0-800e-e501be2b1ea1" />
	</sub-flow>
	<sub-flow name="sf-get-artemis-enterprise-id" doc:id="bc404561-281c-49de-9b2d-ab1d9badc2fa" >
		<http:request method="GET" doc:name="Retrieve from REF_ID" doc:id="af70e898-177a-441e-bd6f-4e7af8041e7a" target="vRefIdResponse" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/REF_ID">
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"OBJECT_TYPE": "ACCOUNT",
	"ARTEMIS_ID" : vars.vCounterpartyId
}]]]></http:query-params>
		</http:request>
		<set-payload value="#[output application/json&#10;---&#10;vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default &quot;&quot;]" doc:name="Set Payload" doc:id="c4d69c35-91c7-44de-9b70-512a805f9102" />
	</sub-flow>
	<sub-flow name="sf-get-artemis-enterprise-id-product" doc:id="a122deeb-c5a7-4f13-b2ae-7410cfd180e4" >
		<http:request method="GET" doc:name="Retrieve from REF_ID" doc:id="b44daa62-4a8a-4148-8d04-eaec66a5b395" target="vRefIdResponse" config-ref="HTTPS_Request_Transaction_DB_SYS_API" path="/api/REF_ID">
			<http:headers ><![CDATA[#[output application/java
---
vars.vRequestAttributes.'headers' default vars.vAttributes.headers]]]></http:headers>
			<http:query-params ><![CDATA[#[output application/java
---
{
	"OBJECT_TYPE": "PRODUCT",
	"ARTEMIS_ID" : vars.vProductId
}]]]></http:query-params>
		</http:request>
		<set-payload value="#[output application/json&#10;---&#10;vars.vRefIdResponse.response[0].'ENTERPRISE_ID' default &quot;&quot;]" doc:name="Set Payload" doc:id="4d919fdf-afbb-4741-b8a7-12fc1952fc3b" />
	</sub-flow>
</mule>
