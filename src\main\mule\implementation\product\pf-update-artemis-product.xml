<?xml version="1.0" encoding="UTF-8"?>

<mule xmlns:http="http://www.mulesoft.org/schema/mule/http" xmlns:db="http://www.mulesoft.org/schema/mule/db"
	xmlns:ee="http://www.mulesoft.org/schema/mule/ee/core"
	xmlns:xero-accounting="http://www.mulesoft.org/schema/mule/xero-accounting" xmlns="http://www.mulesoft.org/schema/mule/core" xmlns:doc="http://www.mulesoft.org/schema/mule/documentation" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.mulesoft.org/schema/mule/core http://www.mulesoft.org/schema/mule/core/current/mule.xsd
http://www.mulesoft.org/schema/mule/xero-accounting http://www.mulesoft.org/schema/mule/xero-accounting/current/mule-xero-accounting.xsd
http://www.mulesoft.org/schema/mule/ee/core http://www.mulesoft.org/schema/mule/ee/core/current/mule-ee.xsd
http://www.mulesoft.org/schema/mule/db http://www.mulesoft.org/schema/mule/db/current/mule-db.xsd
http://www.mulesoft.org/schema/mule/http http://www.mulesoft.org/schema/mule/http/current/mule-http.xsd">
	<flow name="pf-update-artemis-product" doc:id="dc9ba242-b469-435a-b432-4c92dfc21f38" >
		<logger level="INFO" doc:name="LOG INFO: Log Entry"
			doc:id="1ae1b59e-953f-48b2-add3-227333227be7"
			message='#[%dw 2.0&#10;output application/json&#10;--- &#10;{&#10;  	"Message" : "Flow Started", &#10;	"FlowName" : "pf-update-artemis-product", &#10;	"CorrelationID" : vars.vCorrelationId&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Request"
			doc:id="26bcd9e8-0f74-4e07-880e-7f0076e7c45f"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request",&#10;	"FlowName" : "pf-update-artemis-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Request Payload"
			doc:id="80957ec6-a6d4-403b-a9f1-411125e526dc"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Request Payload",&#10;	"FlowName" : "pf-update-artemis-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendRequest": payload&#10;}]' />
		<ee:transform doc:name="Set vDbRecord, vRequestAttributes" doc:id="2136a581-60d3-4d2a-9875-be5f44a2814c" >
			<ee:message >
			</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
import * from dw::core::Arrays
output application/json
---
{
	"CORRELATION_ID": vars.vCorrelationId,
	"OBJECT_TYPE": "PRODUCT",
	"OPERATION": "UPDATE",
	"SOURCE": vars.vAttributes.headers.'x-source' default null,
	"TASK_PAYLOAD": write(payload, "application/json") default null,
	"DESTINATION": "ARTEMIS",
	"STATUS": "IN_PROGRESS",
	"RETRY_COUNT": 0,
	"LAST_UPDATED_BY": "SYSTEM_API",
	"QUERY_PARAMS": ((((vars.vAttributes.queryParams mapObject() -> {
	    "key": ($$),
	    "val": ($)
	}) default {} pluck($) divideBy(2)) map() -> (($) joinBy "|")) joinBy "||") default null,
	"ERROR_MSG": null,
    "ERROR_TYPE": null
}]]></ee:set-variable>
				<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
{
	"headers": {
		"x-source": vars.vAttributes.headers.'x-source' default "",
		"x-transactionId": vars.vTransactionId,
		"x-msg-timestamp": now() as DateTime,
		"correlationId": vars.vCorrelationId,
		"sourceId": "ARTEMIS_SYS_API",
		"destinationId": "TRANSACTION_DB_SYS_API",
		"content-type": "application/json"
	}
}]]></ee:set-variable>
			</ee:variables>
		</ee:transform>
		<ee:transform doc:name="Set vProductId, vEnterpriseId" doc:id="da420125-2b43-4333-8dc5-3aaf568da763">
			<ee:message>
			</ee:message>
			<ee:variables>
				<ee:set-variable variableName="vEnterpriseId"><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'eid' default ""]]></ee:set-variable>
				<ee:set-variable variableName="vProductId" ><![CDATA[%dw 2.0
output application/json
---
attributes.queryParams.'productID' default ""]]></ee:set-variable>
			
</ee:variables>
		</ee:transform>
		<choice doc:name="Check header context" doc:id="0e633144-34e4-4007-a9c6-cdfd2f2ccf0a">
			<when expression="#[(vars.vAttributes.headers.'context' ~= &quot;REGULAR&quot;)]">
				<flow-ref doc:name="Flow Reference to sf-insert-into-transaction-task-details-table" doc:id="6c8a02bd-4768-4de9-bc32-3ab5dd6edf24" name="sf-insert-into-transaction-task-details-table" />
			</when>
			<when expression="#[((vars.vAttributes.headers.'context' ~= &quot;ROLLBACK&quot;) or (vars.vAttributes.headers.'context' ~= &quot;RETRY&quot;) or (vars.vAttributes.headers.'context' ~= &quot;PARTIAL_UPDATE&quot;))]">
				<logger level="INFO" doc:name="LOG INFO: Skip STATUS update" doc:id="49d27926-47fe-4972-ac4f-39c2409446b1" message="#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	&quot;Message&quot; : &quot;No update is made to TRANSACTION_TASK_DETAILS table as headers context is &quot; ++ (vars.vAttributes.headers.'context' default &quot;&quot;),&#10;	&quot;FlowName&quot; : &quot;pf-update-artemis-account&quot;,&#10;	&quot;CorrelationID&quot; : vars.vCorrelationId&#10;}]" />
			</when>
			<otherwise>
				<raise-error doc:name="CUSTOM:INVALID_HEADER_CONTEXT" doc:id="22edfa6f-36ee-48a6-a1a8-cda68259208f" type="CUSTOM:INVALID_HEADER_CONTEXT" description="#[(&quot;Invalid value for context &quot; ++ (vars.vAttributes.headers.'context' default &quot;null&quot;) ++ &quot; is passed&quot;)]" />
			</otherwise>
		</choice>
		<choice doc:name="Check if id is empty" doc:id="f8265bed-5179-4710-ae2d-a6317c2962d5">
			<when expression="#[isEmpty(vars.vProductId)]">
				<flow-ref doc:name="Flow Reference to sf-get-artemis-id" doc:id="0b9b4580-b842-4009-8bf6-7994c7c2062b" name="sf-get-artemis-id" target="vCounterpartyId" />
			</when>
			<otherwise>
				<flow-ref doc:name="Flow Reference to sf-get-artemis-enterprise-id-product" doc:id="1fdfb9ba-7a1a-4a5b-be06-ae5544aa3e6b" name="sf-get-artemis-enterprise-id-product" target="vEnterpriseId" />
			</otherwise>
		
</choice>
		<ee:transform doc:name="Set vUpdateProductBody" doc:id="018df1f0-a972-40d5-909c-e3b9d7ddd270">
					<ee:message>
					</ee:message>
			<ee:variables >
				<ee:set-variable variableName="vUpdateProductBody" ><![CDATA[%dw 2.0
import * from dw::core::Strings
output application/json
---
if(vars.vAttributes.headers.'context' != "PARTIAL_UPDATE") ([
    {
        "database": "Artemis",
        "schema": "port",
        "table": "tdt_Product",
        "value": [
            {
                //"RetailProductName": payload.product.'RetailName',
                //"ChangeUser": payload.product.'changeUser',
                "Enterprise_ID": payload.product.'eid' default "",
                "SyncStatus": payload.product.syncStatus,
                "SyncMessage": payload.product.syncMessage,
                //"ActiveForCommercial": payload.product.'ActiveForCommercial',
                "whereclause": "ProductID = '" ++ vars.vProductId ++ "'"
            }
        ]
    }
])

else  ([
    {
        "database": "Artemis",
        "schema": "port",
        "table": "tdt_Product",
        "value": [
            {
                //"RetailProductName": payload.product.'RetailName',
                //"ChangeUser": payload.product.'changeUser',
                ("Enterprise_ID": payload.product.'eid' default "")  if(payload.product.'eid' != null) ,
                ("SyncStatus": payload.product.syncStatus) if(payload.product.syncStatus != null),
                ("SyncMessage": payload.product.syncMessage)  if(payload.product.syncMessage != null),
                //"ActiveForCommercial": payload.product.'ActiveForCommercial',
                "whereclause": "ProductID = '" ++ vars.vProductId ++ "'"
            }
        ]
    }
])]]></ee:set-variable>
			</ee:variables>
				
</ee:transform>
		<try doc:name="Try" doc:id="ced17c8a-0123-4b87-a153-a42726d01ddb" >
			<set-variable value='#["0" as Number]' doc:name="vRetryCount" doc:id="bd806d58-b6cd-4b99-aa7b-e5b4ccba7b20" variableName="vRetryCount" />
			<until-successful maxRetries="${artemis.retry.maxRetries}" doc:name="Until Successful" doc:id="eabed5e5-9bf6-4f79-8f35-708609e42d68" millisBetweenRetries="${artemis.retry.timePeriod}">
				<try doc:name="Try" doc:id="861904ca-64c3-44d8-a0df-1fe10357eb20">
					<!-- <db:update doc:name="Update Counterparty record" doc:id="d04efbda-937a-4a1a-bffd-bbc10f4bc6e0" config-ref="Database_Config_Transactions" target="vUpdateCounterpartyResponse">
						<db:sql ><![CDATA[#[vars.vUpdateArtemisQuery]]]></db:sql>
					</db:update> -->
					<logger level="INFO" doc:name="Before Updating EID in Artemis Prouct" doc:id="6c0255e8-cde5-4ae9-8ddd-9f4f2740f086" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Before Updating EID in Artemis Product",&#10;	"FlowName": "pf-update-artemis-product",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"RequestData": vars.vUpdateProductBody&#10;}]'/>					
					<http:request method="POST" doc:name="Update Product record in Artemis" doc:id="5cd57f3c-cccc-4c55-aaa8-bc70e209a1af" config-ref="HTTPS_Request_UDA_Endpoint" path="#[output application/json&#10;---&#10;&quot;/UDA?database=&quot; ++ p('secure::https.request.uda.endpoint.postDatabase') ++ &quot;&amp;schema=&quot; ++ p('https.request.uda.endpoint.postSchema') ++ &quot;&amp;objectName=&quot; ++ p('https.request.uda.endpoint.updateObjectName')]" target="vUpdateProductResponse">
						<http:body ><![CDATA[#[vars.vUpdateProductBody]]]></http:body>
					</http:request>
					
					<logger level="INFO" doc:name="After Updating EID in Artemis Product" doc:id="7dbe6d90-8e88-48a2-aa06-9ffa745f0786" message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "After Updating EID in Artemis Product",&#10;	"FlowName": "pf-update-artemis-product",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"ResponseData": vars.vUpdateProductResponse&#10;}]'/>				
					<set-variable value='#[output application/json&#10;var updateResponse = read(vars.vUpdateProductResponse, "application/json")&#10;---&#10;updateResponse.Table[0].Column1 ~= "Commands completed successfully."]' doc:name="vUpdationFlag" doc:id="c9847068-7f78-4a28-97c9-40d651ce50f8" variableName="vUpdationFlag"/>
					<choice doc:name="Check if Artemis updation is succssful" doc:id="e50e12be-4be6-4c20-85bd-eae8463b794b">
				<when expression="#[!(vars.vUpdationFlag)]">
					<logger level="WARN" doc:name="LOG WARN: Updation failed" doc:id="0ba8efa3-132a-4c7d-a012-c221c1f88c90" message="#[var updateResponse = read(vars.vUpdateProductResponse, &quot;application/json&quot;)&#10;output application/json&#10;---&#10;{ 	&#10;	&quot;Message&quot;: &quot;Log Outbound Request&quot;,&#10;	&quot;FlowName&quot;: &quot;pf-update-artemis-product&quot;,&#10;	&quot;CorrelationID&quot;: vars.vCorrelationId,&#10;	&quot;message&quot;: &quot;Error occured while updating record with Product RetailName: &quot; ++ (payload.product.'RetailName' default &quot;&quot;),&#10;	&quot;description&quot;: (updateResponse.Table[0].Column1 default updateResponse.Table[0].Error) default &quot;&quot;&#10;}]" />
				</when>
			</choice>
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="9bcca6e3-437b-4b0b-9421-8742912d08c6" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[%dw 2.0
import * from dw::core::Strings
var updateResponse = read(vars.vUpdateProductResponse, "application/json")
output application/json
---
if(vars.vUpdationFlag) (
	vars.vDbRecord update {
		case STATUS at .STATUS ->  "COMPLETED"
	    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
	}
)
else (
	vars.vDbRecord update {
		case STATUS at .STATUS ->  "FAILED"
	    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
	    case ERROR_MSG at .ERROR_MSG -> ((updateResponse.Table[0].Column1 default updateResponse.Table[0].Error) default "")  replace "\"" with ""
	    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
	}
)]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					
					<error-handler>
						<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="0186f449-5e86-4a3f-b8dc-ada283e83326" type="HTTP:CONNECTIVITY, HTTP:TIMEOUT">
							<set-variable value="#[(vars.vRetryCount as Number) + 1]" doc:name="Update vRetryCount" doc:id="81abe362-c7ed-41aa-af9a-ed580770e708" variableName="vRetryCount" />
						</on-error-propagate>
						<on-error-continue enableNotifications="true" logException="true" doc:name="On Error Continue" doc:id="ed0211ba-dcb5-47fb-b0b1-ef209977cdd1" type="HTTP:BAD_REQUEST, HTTP:CLIENT_SECURITY, HTTP:FORBIDDEN, HTTP:UNAUTHORIZED">
							<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="edb306f1-4be7-464b-813f-3ab79ebb4ed0" >
								<ee:message >
								</ee:message>
								<ee:variables >
									<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
	case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> vars.vRetryCount as Number   
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "DATA"
}]]></ee:set-variable>
									<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
								</ee:variables>
							</ee:transform>
						</on-error-continue>
					</error-handler>
				</try>
			</until-successful>
			<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="59469487-f5d2-4b96-8aef-8c4680c958fd" name="sf-update-transaction-task-details-table"/>
			<ee:transform doc:name="Set payload, httpStatus" doc:id="f3296dca-b3d5-4cc1-98c5-492b126b88ee" >
				<ee:message >
					<ee:set-payload ><![CDATA[output application/json
---
if((vars.vDbRecord.'STATUS' ~= "COMPLETED") and (vars.vUpdationFlag)) {
  "code": 200,
  "transactionId": vars.vTransactionId,
  "status": "SUCCESS",
  "response": {
    "productID": vars.vProductId,
    "eid": vars.vEnterpriseId
  }
}
else {
    "code": 400,
    "status": "FAILURE",
    "transactionId": vars.vTransactionId,
    "response": {
      "message": vars.vDBRecord.'ERROR_TYPE',
      "details": vars.vDBRecord.'ERROR_MSG'
    }
}]]></ee:set-payload>
				</ee:message>
				<ee:variables >
					<ee:set-variable variableName="httpStatus" ><![CDATA[%dw 2.0
output application/json
---
if(vars.vUpdationFlag default false) 200 else 400]]></ee:set-variable>
				</ee:variables>
			</ee:transform>
			<error-handler >
				<on-error-propagate enableNotifications="true" logException="true" doc:name="On Error Propagate" doc:id="887c8e8b-00b8-49bc-8824-f21946685ecc" type="ANY">
					<ee:transform doc:name="Update vDbRecord, vRequestAttributes" doc:id="8753e6fe-03a6-40d5-a2f7-5a710d383f3d" >
						<ee:message >
						</ee:message>
						<ee:variables >
							<ee:set-variable variableName="vDbRecord" ><![CDATA[output application/json
---
vars.vDbRecord update {
    case STATUS at .STATUS ->  "FAILED"
    case RETRY_COUNT at .RETRY_COUNT -> p('artemis.retry.maxRetries') as Number
    case ERROR_MSG at .ERROR_MSG -> error.description replace "\"" with ""
    case ERROR_TYPE at .ERROR_TYPE -> "SYSTEM"
}]]></ee:set-variable>
							<ee:set-variable variableName="vRequestAttributes" ><![CDATA[%dw 2.0
output application/json
---
vars.vRequestAttributes update {
	case timestamp at .headers.'x-msg-timestamp' -> now()
}]]></ee:set-variable>
						</ee:variables>
					</ee:transform>
					<flow-ref doc:name="Flow Reference to sf-update-transaction-task-details-table" doc:id="fe872b9d-d794-4c0d-8e2a-538cf34a8cc9" name="sf-update-transaction-task-details-table" />
				</on-error-propagate>
			</error-handler>
		</try>
		<logger level="DEBUG"
			doc:name="LOG DEBUG: Log Outbound Response Payload"
			doc:id="f989ed4f-063b-4022-9946-b832355b6da3"
			message='#[%dw 2.0&#10;output application/json &#10;---&#10;{ 	&#10;	"Message" : "Log Outbound Resonse Payload",&#10;	"FlowName" : "pf-update-artemis-product",&#10;	"CorrelationID" : vars.vCorrelationId,&#10;	"Endpoint": "/api/product",&#10;	"BackendResponse": payload&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Outbound Response"
			doc:id="51023098-1e81-4ce0-87a3-d01040b3564f"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Log Outbound Response",&#10;	"FlowName": "pf-update-artemis-product",&#10;	"CorrelationID": vars.vCorrelationId,&#10;	"Endpoint": "/api/product"&#10;}]' />
		<logger level="INFO" doc:name="LOG INFO: Log Exit"
			doc:id="c0bdf248-988e-4ff7-ba24-f6fee853d799"
			message='#[%dw 2.0&#10;output application/json&#10;---&#10;{&#10;	"Message": "Flow Ended",&#10;	"FlowName": "pf-update-artemis-product",&#10;	"CorrelationID": vars.vCorrelationId&#10;}]' />
	</flow>
</mule>
